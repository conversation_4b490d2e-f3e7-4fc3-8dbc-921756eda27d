{"extends": "@tsconfig/react-native/tsconfig.json", "compilerOptions": {"baseUrl": ".", "paths": {"@components": ["src/components/index.ts"], "@screens/*": ["src/screens/*"], "@utils": ["src/utils/index.ts"], "@theme": ["src/theme/index.ts"], "@assets/*": ["src/assets/*"], "@global/*": ["src/global/*"], "@context/*": ["src/context/*"], "@routes": ["src/routes/index.routes.tsx"], "@routes.types": ["src/routes/routes.types.ts"], "@environment": ["environment.ts"], "@services/*": ["src/services/*"], "@store/*": ["src/store/*"], "@database/*": ["src/database/*"], "@layouts": ["src/layouts/index.ts"]}}}