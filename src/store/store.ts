import AsyncStorage from '@react-native-async-storage/async-storage';
import { configureStore } from '@reduxjs/toolkit';
import {
  persistReducer,
  FLUSH,
  REHYDRATE,
  PAUSE,
  PERSIST,
  PURGE,
  REGISTER,
  persistStore,
} from 'redux-persist';

// reducers
import authReducer from './slices/auth/auth.slice';
import collectReducer from './slices/collect/collect.slice';
import practiceReducer from './slices/practice/practice.slice';
import dateControlReducer from './slices/dateControl/dateControl.slice';
import infoReducer from './slices/info/info.slice';

const reduxPersistAcions = [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER];
const persistConfig = { key: 'root', storage: AsyncStorage };
const persistCollectConfig = { key: 'collect', storage: AsyncStorage };
const persistPracticeConfig = { key: 'practice', storage: AsyncStorage };
const persisteDateControlConfig = { key: 'dateControl', storage: AsyncStorage };

const persistedReducer = persistReducer(persistConfig, authReducer);
const persistedCollectReducer = persistReducer(
  persistCollectConfig,
  collectReducer,
);
const persistedPracticeReducer = persistReducer(
  persistPracticeConfig,
  practiceReducer,
);
const persistedDateControlReducer = persistReducer(
  persisteDateControlConfig,
  dateControlReducer,
);

const store = configureStore({
  reducer: {
    authReducer: persistedReducer,
    collectReducer: persistedCollectReducer,
    practiceReducer: persistedPracticeReducer,
    dateControlReducer: persistedDateControlReducer,
    infoReducer,
  },
  middleware: getDefaultMiddleware =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [...reduxPersistAcions],
      },
    }),
});
const persistor = persistStore(store);

export type RootStateType = ReturnType<typeof store.getState>;
export type AppDispatchType = typeof store.dispatch;

export { persistor };
export default store;
