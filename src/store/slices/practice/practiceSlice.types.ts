import {
  IPracticeDataStage1,
  IPracticeDataStage2,
  IVisitInformation,
} from '@global/types/visitInformation.types';

interface IPracticeSlice {
  practice: Array<IVisitInformation>;
}

interface IPracticePayload {
  propertyId: number;
  homogeneousAreaId: number;
  practice: IPracticeDataStage1 & IPracticeDataStage2;
}

interface IUsePractice extends IPracticeSlice {
  initializeCurrentPractice: (
    payload: Omit<IPracticePayload, 'practice'>,
  ) => void;
  saveCurrentPracticeArea: (payload: IPracticePayload) => void;
  resetPracticeCollect: () => void;
}

export type { IPracticeSlice, IPracticePayload, IUsePractice };
