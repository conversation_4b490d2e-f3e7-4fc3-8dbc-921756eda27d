import { createSlice } from '@reduxjs/toolkit';
import { collect } from '@utils';
import { IVisitInformation } from '@global/types/visitInformation.types';
import { IPracticePayload, IPracticeSlice } from './practiceSlice.types';

const initialState: IPracticeSlice = {
  practice: [] as Array<IVisitInformation>,
};

const slice = createSlice({
  name: 'practice',
  initialState,
  reducers: {
    initializeCurrentPractice: (
      state,
      action: { payload: Omit<IPracticePayload, 'practice'> },
    ) => {
      const { propertyId, homogeneousAreaId } = action.payload;
      let propertyIdx = -1;
      let homogeneousAreaIdx = -1;

      if (state.practice.length > 0) {
        propertyIdx = collect.findPropertyIndex(propertyId, state.practice);
        homogeneousAreaIdx = collect.findHomogeneousAreaIndex(
          propertyId,
          homogeneousAreaId,
          state.practice,
        );
      }

      if (propertyIdx === -1) {
        state.practice.push({
          property_id: propertyId,
          homogeneous_area: [
            { homogeneous_area_id: homogeneousAreaId, visit_information: {} },
          ],
        } as IVisitInformation);
        return state;
      }

      if (homogeneousAreaIdx === -1) {
        state.practice[propertyIdx].homogeneous_area[
          state.practice[propertyIdx].homogeneous_area.length
        ] = {
          homogeneous_area_id: homogeneousAreaId,
          visit_information: {},
        };
        return state;
      }
    },
    saveCurrentPracticeArea: (state, action: { payload: IPracticePayload }) => {
      const { propertyId, homogeneousAreaId, practice } = action.payload;
      let propertyIdx = -1;
      let homogeneousAreaIdx = -1;

      if (state.practice.length > 0) {
        propertyIdx = collect.findPropertyIndex(propertyId, state.practice);
        homogeneousAreaIdx = collect.findHomogeneousAreaIndex(
          propertyId,
          homogeneousAreaId,
          state.practice,
        );
      }

      if (propertyIdx !== -1 && homogeneousAreaIdx !== -1) {
        state.practice[propertyIdx].homogeneous_area[
          homogeneousAreaIdx
        ].visit_information = {
          ...state.practice[propertyIdx].homogeneous_area[homogeneousAreaIdx]
            .visit_information,
          ...practice,
        };
      }
      return state;
    },
    resetPracticeCollect: () => initialState,
  },
});

export const {
  initializeCurrentPractice,
  saveCurrentPracticeArea,
  resetPracticeCollect,
} = slice.actions;

export default slice.reducer;
