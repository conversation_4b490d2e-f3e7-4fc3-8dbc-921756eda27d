import { useDispatch, useSelector } from 'react-redux';
import { AppDispatchType, RootStateType } from '@store/store';
import { IPracticePayload, IUsePractice } from './practiceSlice.types';
import {
  initializeCurrentPractice,
  resetPracticeCollect,
  saveCurrentPracticeArea,
} from './practice.slice';

function usePractice(): IUsePractice {
  const dispatch = useDispatch<AppDispatchType>();
  const { practice } = useSelector(
    (state: RootStateType) => state.practiceReducer,
  );

  return {
    practice,
    initializeCurrentPractice: (payload: Omit<IPracticePayload, 'practice'>) =>
      dispatch(initializeCurrentPractice(payload)),
    saveCurrentPracticeArea: (payload: IPracticePayload) =>
      dispatch(saveCurrentPracticeArea(payload)),
    resetPracticeCollect: () => dispatch(resetPracticeCollect()),
  };
}

export { usePractice };
