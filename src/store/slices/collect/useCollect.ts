import { useDispatch, useSelector } from 'react-redux';
import { AppDispatchType, RootStateType } from '@store/store';
import {
  initializeCurrentCollect,
  resetCollect,
  saveCurrentCollect,
} from './collect.slice';
import { ICollectPayload, IUseCollect } from './collectSlice.types';

function useCollect(): IUseCollect {
  const dispatch = useDispatch<AppDispatchType>();
  const { collect } = useSelector(
    (state: RootStateType) => state.collectReducer,
  );

  return {
    collect,
    initializeCurrentCollect: (payload: Omit<ICollectPayload, 'collect'>) =>
      dispatch(initializeCurrentCollect(payload)),
    saveCurrentCollect: (payload: ICollectPayload) =>
      dispatch(saveCurrentCollect(payload)),
    resetCollect: () => dispatch(resetCollect()),
  };
}

export { useCollect };
