import { createSlice } from '@reduxjs/toolkit';
import { collect } from '@utils';
import { ICollectData } from '@global/types/collect.types';
import { ICollectPayload, ICollectSlice } from './collectSlice.types';

const initialState: ICollectSlice = {
  collect: [] as Array<ICollectData>,
};

const slice = createSlice({
  name: 'collect',
  initialState,
  reducers: {
    initializeCurrentCollect: (
      state,
      action: { payload: Omit<ICollectPayload, 'collect'> },
    ) => {
      const { stratumId, samplingPointId } = action.payload;
      let stratumIdx = -1;
      let samplingPointIdx = -1;
      if (state.collect.length > 0) {
        stratumIdx = collect.findStratumIndex(stratumId, state.collect);
        samplingPointIdx = collect.findSamplingPointIndex(
          stratumId,
          samplingPointId,
          state.collect,
        );
      }

      if (stratumIdx === -1) {
        state.collect.push({
          stratum_id: stratumId,
          sampling_points: [
            {
              sampling_point_id: samplingPointId,
              trees: [],
            },
          ],
        });
      }

      if (samplingPointIdx === -1) {
        const _stratumIdx = stratumIdx === -1 ? 0 : stratumIdx;
        state.collect[_stratumIdx].sampling_points.push({
          sampling_point_id: samplingPointId,
          trees: [],
        });
      }
    },
    saveCurrentCollect: (state, action: { payload: ICollectPayload }) => {
      const {
        stratumId,
        samplingPointId,
        collect: collectData,
      } = action.payload;
      let stratumIdx = -1;
      let samplingPointIdx = -1;

      if (state.collect.length > 0) {
        stratumIdx = collect.findStratumIndex(stratumId, state.collect);
        samplingPointIdx = collect.findSamplingPointIndex(
          stratumId,
          samplingPointId,
          state.collect,
        );
      }

      if (stratumIdx !== -1 && samplingPointIdx !== -1) {
        const treeWasVisited = collect.checkTreeWasVisited(
          collectData.tree_id,
          stratumId,
          samplingPointId,
          state.collect,
        );

        if (!treeWasVisited) {
          state.collect[stratumIdx].sampling_points[
            samplingPointIdx
          ].trees.push(collectData);
        } else {
          const parsedData = state.collect[stratumIdx].sampling_points[
            samplingPointIdx
          ].trees.map(tree => {
            if (tree.tree_id === collectData.tree_id) {
              return collectData;
            }
            return tree;
          });
          state.collect[stratumIdx].sampling_points[samplingPointIdx].trees =
            parsedData;
        }
      }
    },
    resetCollect: () => {
      return initialState;
    },
  },
});

export const { initializeCurrentCollect, saveCurrentCollect, resetCollect } =
  slice.actions;
export default slice.reducer;
