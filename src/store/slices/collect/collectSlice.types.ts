import { ICollectData } from '@global/types/collect.types';

interface ICollectPayload {
  stratumId: number;
  samplingPointId: number;
  collect: {
    tree_id: number;
    data: {
      adult: number;
      adult2: number;
      medium: number;
      medium2: number;
      medium3: number;
      mature: number;
      mature2: number;
      mature3: number;
      mature4: number;
      bobbin: number;
      bobbinPV: number;
      small: number;
    };
  };
}

interface ICollectSlice {
  collect: Array<ICollectData>;
}

interface IUseCollect extends ICollectSlice {
  initializeCurrentCollect: (payload: Omit<ICollectPayload, 'collect'>) => void;
  saveCurrentCollect: (payload: ICollectPayload) => void;
  resetCollect: () => void;
}

export type { ICollectPayload, ICollectSlice, IUseCollect };
