interface IInfoSlice {
  propertyId: number;
  homogeneousAreaId: number;
  stratumId: number;
  samplingPointId: number;
  treeId: number;
  visitId: number;
  anyTreeWasVisited: boolean;
}

interface IUseInfo extends IInfoSlice {
  setPropertyId: (payload: Pick<IInfoSlice, 'propertyId'>) => void;
  setHomogeneousAreaId: (
    payload: Pick<IInfoSlice, 'homogeneousAreaId'>,
  ) => void;
  setStratumId: (payload: Pick<IInfoSlice, 'stratumId'>) => void;
  setSamplingPointId: (payload: Pick<IInfoSlice, 'samplingPointId'>) => void;
  setTreeId: (payload: Pick<IInfoSlice, 'treeId'>) => void;
  setVisitId: (payload: Pick<IInfoSlice, 'visitId'>) => void;
  setAnyTreeWasVisited: (
    payload: Pick<IInfoSlice, 'anyTreeWasVisited'>,
  ) => void;
}

export type { IInfoSlice, IUseInfo };
