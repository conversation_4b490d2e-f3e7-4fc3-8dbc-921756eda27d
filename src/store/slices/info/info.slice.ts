import { createSlice } from '@reduxjs/toolkit';
import { IInfoSlice } from './infoSlice.types';

const initialState: IInfoSlice = {} as IInfoSlice;

const slice = createSlice({
  name: 'info',
  initialState,
  reducers: {
    setPropertyId: (
      state,
      action: { payload: Pick<IInfoSlice, 'propertyId'> },
    ) => {
      return {
        ...state,
        propertyId: action.payload.propertyId,
      };
    },
    setHomogeneousAreaId: (
      state,
      action: { payload: Pick<IInfoSlice, 'homogeneousAreaId'> },
    ) => {
      return {
        ...state,
        homogeneousAreaId: action.payload.homogeneousAreaId,
      };
    },
    setStratumId: (
      state,
      action: { payload: Pick<IInfoSlice, 'stratumId'> },
    ) => {
      return {
        ...state,
        stratumId: action.payload.stratumId,
      };
    },
    setSamplingPointId: (
      state,
      action: { payload: Pick<IInfoSlice, 'samplingPointId'> },
    ) => {
      return {
        ...state,
        samplingPointId: action.payload.samplingPointId,
      };
    },
    setTreeId: (state, action: { payload: Pick<IInfoSlice, 'treeId'> }) => {
      return {
        ...state,
        treeId: action.payload.treeId,
      };
    },
    setVisitId: (state, action: { payload: Pick<IInfoSlice, 'visitId'> }) => {
      return {
        ...state,
        visitId: action.payload.visitId,
      };
    },
    setAnyTreeWasVisited: (
      state,
      action: { payload: Pick<IInfoSlice, 'anyTreeWasVisited'> },
    ) => {
      return {
        ...state,
        anyTreeWasVisited: action.payload.anyTreeWasVisited,
      };
    },
  },
});

export const {
  setPropertyId,
  setHomogeneousAreaId,
  setStratumId,
  setSamplingPointId,
  setTreeId,
  setVisitId,
  setAnyTreeWasVisited,
} = slice.actions;

export default slice.reducer;
