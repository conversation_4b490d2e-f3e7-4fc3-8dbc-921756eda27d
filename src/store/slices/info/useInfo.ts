import { useDispatch, useSelector } from 'react-redux';
import { IInfoSlice, IUseInfo } from './infoSlice.types';
import { AppDispatchType, RootStateType } from '@store/store';
import {
  setAnyTreeWasVisited,
  setHomogeneousAreaId,
  setPropertyId,
  setSamplingPointId,
  setStratumId,
  setTreeId,
  setVisitId,
} from './info.slice';

function useInfo(): IUseInfo {
  const dispatch = useDispatch<AppDispatchType>();
  const infoSelector = useSelector((state: RootStateType) => state.infoReducer);

  return {
    ...infoSelector,
    setPropertyId: (payload: Pick<IInfoSlice, 'propertyId'>) =>
      dispatch(setPropertyId(payload)),
    setHomogeneousAreaId: (payload: Pick<IInfoSlice, 'homogeneousAreaId'>) =>
      dispatch(setHomogeneousAreaId(payload)),
    setStratumId: (payload: Pick<IInfoSlice, 'stratumId'>) =>
      dispatch(setStratumId(payload)),
    setSamplingPointId: (payload: Pick<IInfoSlice, 'samplingPointId'>) =>
      dispatch(setSamplingPointId(payload)),
    setTreeId: (payload: Pick<IInfoSlice, 'treeId'>) =>
      dispatch(setTreeId(payload)),
    setVisitId: (payload: Pick<IInfoSlice, 'visitId'>) =>
      dispatch(setVisitId(payload)),
    setAnyTreeWasVisited: (payload: Pick<IInfoSlice, 'anyTreeWasVisited'>) =>
      dispatch(setAnyTreeWasVisited(payload)),
  };
}

export { useInfo };
