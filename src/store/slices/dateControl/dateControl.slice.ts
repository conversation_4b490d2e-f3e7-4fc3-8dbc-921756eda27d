import { createSlice } from '@reduxjs/toolkit';
import { IPracticeControl } from '@global/types/practice.types';
import { ICollectControl } from '@global/types/collect.types';
import {
  IDateControlPayload,
  IDateControlSlice,
} from './dateControlSlice.types';
import { collect } from '@utils';

const initialState: IDateControlSlice = {
  practiceControl: [] as Array<IPracticeControl>,
  collectControl: [] as Array<ICollectControl>,
};

const slice = createSlice({
  name: 'dateControl',
  initialState,
  reducers: {
    setLastPracticeCollectDate: (
      state,
      action: {
        payload: Pick<IDateControlPayload, 'homogeneousAreaId' | 'date'>;
      },
    ) => {
      const { homogeneousAreaId, date } = action.payload;
      let areaIdx = -1;
      const _practiceControl = [...state.practiceControl];

      if (state.practiceControl.length > 0) {
        areaIdx = collect.findAreaIndex(
          homogeneousAreaId,
          state.practiceControl,
        );
      }

      if (areaIdx === -1) {
        _practiceControl[_practiceControl.length] = {
          ..._practiceControl[_practiceControl.length],
          homogeneous_area_id: homogeneousAreaId,
          previous: date,
        };

        return {
          practiceControl: _practiceControl,
          collectControl: [...state.collectControl],
        };
      }

      _practiceControl[areaIdx] = {
        ..._practiceControl[areaIdx],
        previous: date,
      };
      return {
        practiceControl: _practiceControl,
        collectControl: [...state.collectControl],
      };
    },
    setLastFruitCollectDate: (
      state,
      action: {
        payload: Pick<IDateControlPayload, 'samplingPointId' | 'date'>;
      },
    ) => {
      const { samplingPointId, date } = action.payload;
      let samplingPointIdx = -1;
      const _collectControl = [...state.collectControl];

      if (state.collectControl.length > 0) {
        samplingPointIdx = collect.findSPIndex(
          samplingPointId,
          state.collectControl,
        );
      }

      if (samplingPointIdx === -1) {
        _collectControl[_collectControl.length] = {
          ..._collectControl[_collectControl.length],
          sampling_point_id: samplingPointId,
          previous: date,
        };
        return {
          collectControl: _collectControl,
          practiceControl: [...state.practiceControl],
        };
      }

      _collectControl[samplingPointIdx] = {
        ..._collectControl[samplingPointIdx],
        previous: date,
      };

      return {
        collectControl: _collectControl,
        practiceControl: [...state.practiceControl],
      };
    },
  },
});

export const { setLastPracticeCollectDate, setLastFruitCollectDate } =
  slice.actions;

export default slice.reducer;
