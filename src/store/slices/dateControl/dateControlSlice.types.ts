import { ICollectControl } from '@global/types/collect.types';
import { IPracticeControl } from '@global/types/practice.types';

interface IDateControlSlice {
  practiceControl: Array<IPracticeControl>;
  collectControl: Array<ICollectControl>;
}

interface IDateControlPayload {
  samplingPointId: number;
  homogeneousAreaId: number;
  date: Date;
}

interface IUseDateControl extends IDateControlSlice {
  setLastPracticeCollectDate: (
    payload: Pick<IDateControlPayload, 'homogeneousAreaId' | 'date'>,
  ) => void;
  setLastFruitCollectDate: (
    payload: Pick<IDateControlPayload, 'samplingPointId' | 'date'>,
  ) => void;
}

export type { IDateControlSlice, IDateControlPayload, IUseDateControl };
