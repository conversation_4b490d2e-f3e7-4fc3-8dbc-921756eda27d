import { useDispatch, useSelector } from 'react-redux';
import { AppDispatchType, RootStateType } from '@store/store';
import { IDateControlPayload, IUseDateControl } from './dateControlSlice.types';
import {
  setLastFruitCollectDate,
  setLastPracticeCollectDate,
} from './dateControl.slice';

function useDateControl(): IUseDateControl {
  const dispatch = useDispatch<AppDispatchType>();
  const { practiceControl, collectControl } = useSelector(
    (state: RootStateType) => state.dateControlReducer,
  );

  return {
    practiceControl,
    collectControl,
    setLastPracticeCollectDate: (
      payload: Pick<IDateControlPayload, 'homogeneousAreaId' | 'date'>,
    ) => dispatch(setLastPracticeCollectDate(payload)),
    setLastFruitCollectDate: (
      payload: Pick<IDateControlPayload, 'samplingPointId' | 'date'>,
    ) => dispatch(setLastFruitCollectDate(payload)),
  };
}

export { useDateControl };
