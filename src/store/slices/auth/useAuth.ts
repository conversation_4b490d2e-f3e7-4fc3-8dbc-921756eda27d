import { useDispatch, useSelector } from 'react-redux';

import { AppDispatchType, RootStateType } from '@store/store';
import { IAuthUserSlice, IUseAuthUser } from './authSlice.types';
import { authenticate, logout } from './auth.slice';

/**
 * Hook personalizado para gerenciar a autenticação do usuário.
 *
 * @function useAuth
 * @returns {IUseAuthUser} Um objeto contendo informações e ações relacionadas à autenticação do usuário.
 */
function useAuth(): IUseAuthUser {
  const dispatch = useDispatch<AppDispatchType>();
  const { user, auth } = useSelector(
    (state: RootStateType) => state.authReducer,
  );

  return {
    user,
    auth,
    authenticate: (payload: IAuthUserSlice) => dispatch(authenticate(payload)),
    logout: () => dispatch(logout()),
  };
}

export { useAuth };
