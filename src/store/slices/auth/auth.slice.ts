import { createSlice } from '@reduxjs/toolkit';
import { IUser } from '@global/types/user.types';
import { IAuthUserSlice } from './authSlice.types';
import { IUserAuth } from '@global/types/auth.types';

const initialState: IAuthUserSlice = {
  auth: {} as IUserAuth,
  user: {} as IUser,
};

const slice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    authenticate: (state, action: { payload: IAuthUserSlice }) => {
      state.user = action.payload.user;
      state.auth = action.payload.auth;
    },
    logout: state => {
      state.user = initialState.user;
    },
  },
});

export const { authenticate, logout } = slice.actions;
export default slice.reducer;
