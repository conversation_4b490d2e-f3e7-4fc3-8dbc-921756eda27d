import { ICollectData } from '@global/types/collect.types';
import { ITreeVisit } from '@global/types/treeVisit.types';

const fruitKeys: Array<keyof ITreeVisit> = [
  'bobbin',
  'small',
  'medium',
  'medium2',
  'medium3',
  'adult',
  'adult2',
  'mature',
  'mature2',
  'mature3',
  'mature4',
];

const unhealthyProperties = [
  'rotten',
  'rat',
  'witchs_broom',
  'loss',
  'piece',
] as const;

/**
 * Encontra o índice de uma propriedade específica em um array de dados de prática.
 *
 * @function findPropertyIndex
 * @param {number} propertyId - O ID da propriedade a ser localizada.
 * @param {Array<any>} practiceData - O array de dados de prática no qual a propriedade deve ser procurada.
 * @returns {number} O índice da propriedade no array de dados de prática ou -1 se não for encontrada.
 */

function findPropertyIndex(
  propertyId: number,
  practiceData: Array<any>,
): number {
  const propertyIndex = practiceData.findIndex(
    practice => practice.property_id === propertyId,
  );
  return propertyIndex;
}

/**
 * Encontra o índice de uma área homogênea específica em uma propriedade dentro de um array de dados de prática.
 *
 * @function findHomogeneousAreaIndex
 * @param {number} propertyId - O ID da propriedade à qual a área homogênea pertence.
 * @param {number} homogeneousAreaId - O ID da área homogênea a ser localizada.
 * @param {Array<any>} practiceData - O array de dados de prática no qual a área homogênea deve ser procurada.
 * @returns {number} O índice da área homogênea na propriedade ou -1 se não for encontrada.
 */
function findHomogeneousAreaIndex(
  propertyId: number,
  homogeneousAreaId: number,
  practiceData: Array<any>,
): number {
  const propertyIndex = findPropertyIndex(propertyId, practiceData);
  if (propertyIndex !== -1) {
    const homogeneousAreaIndex = practiceData[
      propertyIndex
    ].homogeneous_area.findIndex(
      ha => ha.homogeneous_area_id === homogeneousAreaId,
    );
    return homogeneousAreaIndex;
  }

  return propertyIndex;
}

/**
 * Encontra o índice de uma área específica em um array de dados de prática.
 *
 * @function findAreaIndex
 * @param {number} homogeneousAreaId - O ID da área homogênea à qual a área pertence.
 * @param {Array<any>} practiceData - O array de dados de prática no qual a área deve ser procurada.
 * @returns {number} O índice da área no array de dados de prática ou -1 se não for encontrada.
 */
function findAreaIndex(
  homogeneousAreaId: number,
  practiceData: Array<any>,
): number {
  const areaIndex = practiceData.findIndex(
    practice => practice.homogeneous_area_id === homogeneousAreaId,
  );
  return areaIndex;
}

/**
 * Encontra o índice de um estrato específico em um array de dados de coleta.
 *
 * @function findStratumIndex
 * @param {number} stratumId - O ID do estrato a ser localizado.
 * @param {Array<any>} collectData - O array de dados de coleta no qual o estrato deve ser procurado.
 * @returns {number} O índice do estrato no array de dados de coleta ou -1 se não for encontrado.
 */
function findStratumIndex(
  stratumId: number,
  collectData: Array<ICollectData>,
): number {
  const stratumIndex = collectData.findIndex(
    collect => collect.stratum_id === stratumId,
  );
  return stratumIndex;
}

/**
 * Encontra o índice de um ponto de amostragem (Sampling Point) com base em seu ID.
 *
 * @function findSPIndex
 * @param {number} samplingPointId - O ID do ponto de amostragem a ser encontrado.
 * @param {Array<ICollectData>} collectData - Um array de dados de coleta contendo pontos de amostragem.
 * @returns {number} Retorna o índice do ponto de amostragem no array ou -1 se não for encontrado.
 */
function findSPIndex(
  samplingPointId: number,
  collectData: Array<ICollectData>,
): number {
  const samplingPointIndex = collectData.findIndex(
    sp => sp.sampling_point_id === samplingPointId,
  );
  return samplingPointIndex;
}

/**
 * Encontra o índice de um ponto de amostragem específico em um estrato dentro de um array de dados de coleta.
 *
 * @function findSamplingPointIndex
 * @param {number} stratumId - O ID do estrato ao qual o ponto de amostragem pertence.
 * @param {number} samplingPointId - O ID do ponto de amostragem a ser localizado.
 * @param {Array<any>} collectData - O array de dados de coleta no qual o ponto de amostragem deve ser procurado.
 * @returns {number} O índice do ponto de amostragem no estrato ou -1 se não for encontrado.
 */
function findSamplingPointIndex(
  stratumId: number,
  samplingPointId: number,
  collectData: Array<ICollectData>,
): number {
  const stratumIndex = findStratumIndex(stratumId, collectData);

  if (stratumIndex !== -1) {
    const samplingPointIndex = collectData[
      stratumIndex
    ].sampling_points.findIndex(sp => sp.sampling_point_id === samplingPointId);
    return samplingPointIndex;
  }

  return stratumIndex;
}

/**
 * Verifica se a "Área 1" foi observada em uma propriedade e área homogênea específicas.
 *
 * @function checkArea1WasObserved
 * @param {number} propertyId - O ID da propriedade a ser verificada.
 * @param {number} homogeneousAreaId - O ID da área homogênea a ser verificada.
 * @param {Array<any>} practiceData - O array de dados de prática no qual a verificação deve ser realizada.
 * @returns {boolean} Verdadeiro se a "Área 1" foi observada; falso caso contrário.
 */
function checkArea1WasObserved(
  propertyId: number,
  homogeneousAreaId: number,
  practiceData: Array<any>,
): boolean {
  const propertyIndex = findPropertyIndex(propertyId, practiceData);
  const homogeneousAreaIndex = findHomogeneousAreaIndex(
    propertyId,
    homogeneousAreaId,
    practiceData,
  );

  if (propertyIndex === -1 || homogeneousAreaIndex === -1) {
    return false;
  }

  return (
    practiceData[propertyIndex].homogeneousArea[homogeneousAreaIndex]
      .visit_information.renewed !== undefined
  );
}

/**
 * Verifica se a "Área 2" foi observada em uma propriedade e área homogênea específicas.
 *
 * @function checkArea2WasObserved
 * @param {number} propertyId - O ID da propriedade a ser verificada.
 * @param {number} homogeneousAreaId - O ID da área homogênea a ser verificada.
 * @param {Array<any>} practiceData - O array de dados de prática no qual a verificação deve ser realizada.
 * @returns {boolean} Verdadeiro se a "Área 2" foi observada; falso caso contrário.
 */
function checkArea2WasObserved(
  propertyId: number,
  homogeneousAreaId: number,
  practiceData: Array<any>,
): boolean {
  const propertyIndex = findPropertyIndex(propertyId, practiceData);
  const homogeneousAreaIndex = findHomogeneousAreaIndex(
    propertyId,
    homogeneousAreaId,
    practiceData,
  );

  if (propertyIndex === -1 || homogeneousAreaIndex === -1) {
    return false;
  }

  return (
    practiceData[propertyIndex].homogeneousArea[homogeneousAreaIndex]
      .visit_information.date !== undefined
  );
}

/**
 * Verifica se uma árvore específica foi visitada em um ponto de amostragem e estrato
 * particular.
 *
 * @function checkTreeWasVisited
 * @param {number} treeId - O ID da árvore a ser verificada.
 * @param {number} stratumId - O ID do estrato no qual a árvore deve ser verificada.
 * @param {number} samplingPointId - O ID do ponto de amostragem no qual a árvore deve ser verificada.
 * @param {Array<any>} collectData - O array de dados de coleta no qual a verificação deve ser realizada.
 * @returns {boolean} Verdadeiro se a árvore foi visitada; falso caso contrário.
 */
function checkTreeWasVisited(
  treeId: number,
  stratumId: number,
  samplingPointId: number,
  collectData: Array<any>,
): boolean {
  const stratumIndex = findStratumIndex(stratumId, collectData);
  const samplingPointIndex = findSamplingPointIndex(
    stratumId,
    samplingPointId,
    collectData,
  );

  if (stratumIndex === -1 || samplingPointIndex === -1) {
    return false;
  }

  if (
    collectData[stratumIndex].sampling_points[samplingPointIndex].trees.length >
    0
  ) {
    const checkTree = collectData[stratumIndex].sampling_points[
      samplingPointIndex
    ].trees.find(tree => tree.tree_id === treeId);
    return checkTree;
  }
  return false;
}

/**
 * Verifica se a coleta foi iniciada em um ponto de amostragem e estrato específicos.
 *
 * @function checkCollectWasStarted
 * @param {number} stratumId - O ID do estrato no qual a verificação deve ser realizada.
 * @param {number} samplingPointId - O ID do ponto de amostragem no qual a verificação deve ser realizada.
 * @param {Array<any>} collectData - O array de dados de coleta no qual a verificação deve ser realizada.
 * @returns {boolean} Verdadeiro se a coleta foi iniciada; falso caso contrário.
 */
function checkCollectWasStarted(
  stratumId: number,
  samplingPointId: number,
  collectData: Array<any>,
): boolean {
  const stratumIndex = findStratumIndex(stratumId, collectData);
  const samplingPointIndex = findSamplingPointIndex(
    stratumId,
    samplingPointId,
    collectData,
  );

  if (stratumIndex === -1 || samplingPointIndex === -1) {
    return false;
  }

  if (
    collectData[stratumIndex].samplingPoints[samplingPointIndex].trees.length >
    0
  ) {
    return true;
  }
  return false;
}

/**
 * Verifica se todas as árvores designadas foram visitadas em um ponto de amostragem e estrato específicos.
 *
 * @function checkAllTreesWasVisited
 * @param {number} stratumId - O ID do estrato no qual a verificação deve ser realizada.
 * @param {number} samplingPointId - O ID do ponto de amostragem no qual a verificação deve ser realizada.
 * @param {Array<any>} trees - O array de árvores designadas para visitação.
 * @param {Array<any>} collectData - O array de dados de coleta no qual a verificação deve ser realizada.
 * @returns {boolean} Verdadeiro se todas as árvores designadas foram visitadas; falso caso contrário.
 */
function checkAllTreesWasVisited(
  stratumId: number,
  samplingPointId: number,
  trees: Array<any>,
  collectData: Array<any>,
): boolean {
  const stratumIndex = findStratumIndex(stratumId, collectData);
  const samplingPointIndex = findSamplingPointIndex(
    stratumId,
    samplingPointId,
    collectData,
  );

  if (stratumIndex === -1 || samplingPointIndex === -1) {
    return false;
  }

  if (
    collectData[stratumIndex].sampling_points[samplingPointIndex].trees.length >
      0 &&
    trees
  ) {
    trees.length ===
      collectData[stratumIndex].sampling_points[samplingPointIndex].trees
        .length;
  }
  return false;
}

function calculateTotalHealthyFruits(treeVisit: ITreeVisit): number {
  return fruitKeys.reduce((sum, key) => {
    const item = treeVisit[key];
    if (typeof item === 'object' && item !== null && 'total' in item) {
      return sum + (item as { total: number }).total;
    }
    return sum;
  }, 0);
}

function calculateTotalUnhealthyFruits(treeVisit: ITreeVisit): number {
  return fruitKeys.reduce((sum, key) => {
    const fruit = treeVisit[key];
    unhealthyProperties.forEach((property: any) => {
      if (fruit && typeof fruit[property] === 'number') {
        sum += fruit[property];
      }
    });

    return sum;
  }, 0);
}

export const collect = {
  findPropertyIndex,
  findHomogeneousAreaIndex,
  findAreaIndex,
  findStratumIndex,
  findSPIndex,
  findSamplingPointIndex,
  checkArea1WasObserved,
  checkArea2WasObserved,
  checkTreeWasVisited,
  checkCollectWasStarted,
  checkAllTreesWasVisited,
  calculateTotalHealthyFruits,
  calculateTotalUnhealthyFruits,
};
