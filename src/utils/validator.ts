import {
  IAdultStage1,
  IAdultStage2,
  <PERSON><PERSON><PERSON><PERSON>,
  IMatureStage1,
  IMatureStage2,
  IMatureStage3,
  IMatureStage4,
  IMediumStage1,
  IMediumStage2,
  IMediumStage3,
  ISmall,
} from '@global/types/cocoa.types';
import { ICheckQuantityFunc, ICollect } from '@global/types/collect.types';

/**
 * Verifica a quantidade de bilros de acordo com os valores anteriores e atuais.
 *
 * @function validateBilros
 * @param {IBilro} bilrosCurrentVisit - Os valores anteriores de bilros.
 * @param {IBilro} bilrosPreviousVisit - Os valores atuais de bilros por ponto de venda.
 * @returns {ICheckQuantityFunc} Um objeto contendo informações sobre a verificação.
 * @property {boolean} isError - Indica se há um erro na quantidade de bilros.
 * @property {string} [title] - O título do erro, se houver.
 * @property {string} [message] - A mensagem de erro, se houver.
 */
function validateBilros(
  bilrosPreviousVisit: IBilro,
  bilrosCurrentVisit: IBilro,
): ICheckQuantityFunc {
  const totalBilrosCurrentVisit = bilrosCurrentVisit.total;
  const totalBilrosPreviousVisit = bilrosPreviousVisit.total;

  if (
    totalBilrosCurrentVisit === totalBilrosPreviousVisit ||
    totalBilrosCurrentVisit > totalBilrosPreviousVisit
  ) {
    return {
      isError: false,
      quantity: totalBilrosPreviousVisit,
    };
  }

  return {
    isError: true,
    title: 'Verifique o valor editado de 0-21 novamente',
    message:
      'A quantidade de frutos é incorreta em comparação com o total anterior.',
  };
}

/**
 * Valida a quantidade de frutos pequenos na coleta atual.
 *
 * @function validateSmalls
 * @param {ISmall} smallsPreviousVisit - Os dados dos frutos pequenos na coleta anterior.
 * @param {ISmall} smallsCurrentVisit - Os dados dos frutos pequenos na coleta atual.
 * @param {number} bilroToSmall - A quantidade de frutos do tipo "bilro" para frutos pequenos na coleta anterior.
 * @returns {ICheckQuantityFunc} O resultado da validação, indicando se há erros na quantidade de frutos pequenos.
 */
function validateSmalls(
  smallsPreviousVisit: ISmall,
  smallsCurrentVisit: ISmall,
  bilroToSmall: number,
): ICheckQuantityFunc {
  const lostFruits =
    smallsCurrentVisit.loss +
    smallsCurrentVisit.piece +
    smallsCurrentVisit.witchs_broom;

  const smallsToMedium = smallsPreviousVisit.total;
  const expectedTotalCurrentVisit = bilroToSmall - lostFruits;

  if (
    expectedTotalCurrentVisit === smallsCurrentVisit.total ||
    expectedTotalCurrentVisit < smallsCurrentVisit.total
  ) {
    return {
      isError: false,
      quantity: smallsToMedium,
    };
  }

  return {
    isError: true,
    title: 'Realize a verificação de 21-42 novamente',
    message: 'Quantidade de frutos incorreta,ver coleta anterior.',
  };
}

/**
 * Valida a quantidade de frutos no estágio médio 1 na coleta atual.
 *
 * @function validateMediumStage1
 * @param {IMediumStage1} mediumPreviousVisit - Os dados do estágio médio 1 na coleta anterior.
 * @param {IMediumStage1} mediumCurrentVisit - Os dados do estágio médio 1 na coleta atual.
 * @param {number} smallsToMedium - A quantidade de frutos pequenos para o estágio médio na coleta anterior.
 * @returns {ICheckQuantityFunc} O resultado da validação, indicando se há erros na quantidade de frutos no estágio médio 1.
 */
function validateMediumStage1(
  mediumPreviousVisit: IMediumStage1,
  mediumCurrentVisit: IMediumStage1,
  smallsToMedium: number,
): ICheckQuantityFunc {
  const lostFruits = mediumCurrentVisit.loss + mediumCurrentVisit.piece;
  const medium1ToMedium2 = mediumPreviousVisit.total;
  const expectedTotalCurrentVisit = smallsToMedium - lostFruits;

  if (expectedTotalCurrentVisit === mediumCurrentVisit.total) {
    return {
      isError: false,
      quantity: medium1ToMedium2,
    };
  }

  return {
    isError: true,
    title: 'Realize a verificação de 42-63 novamente',
    message: 'Quantidade de frutos incorreta, ver coleta anterior',
  };
}

/**
 * Valida a quantidade de frutos no estágio médio 2 na coleta atual.
 *
 * @function validateMediumStage2
 * @param {IMediumStage2} mediumStage2PreviousVisit - Os dados do estágio médio 2 na coleta anterior.
 * @param {IMediumStage2} mediumStage2CurrentVisit - Os dados do estágio médio 2 na coleta atual.
 * @param {number} medium1ToMedium2 - A quantidade de frutos do estágio médio 1 para o estágio médio 2 na coleta anterior.
 * @returns {ICheckQuantityFunc} O resultado da validação, indicando se há erros na quantidade de frutos no estágio médio 2.
 */
function validateMediumStage2(
  mediumStage2PreviousVisit: IMediumStage2,
  mediumStage2CurrentVisit: IMediumStage2,
  medium1ToMedium2: number,
): ICheckQuantityFunc {
  const lostFruits =
    mediumStage2CurrentVisit.loss +
    mediumStage2CurrentVisit.piece +
    mediumStage2CurrentVisit.rat +
    mediumStage2CurrentVisit.rotten +
    mediumStage2CurrentVisit.witchs_broom;
  const medium2ToMedium3 = mediumStage2PreviousVisit.total;
  const expectedTotalCurrentVisit = medium1ToMedium2 - lostFruits;

  if (expectedTotalCurrentVisit === mediumStage2CurrentVisit.total) {
    return {
      isError: false,
      quantity: medium2ToMedium3,
    };
  }

  return {
    isError: true,
    title: 'Realize a verificação de 63-84 novamente',
    message: 'Quantidade de frutos incorreta, ver coleta anterior',
  };
}

/**
 * Valida a quantidade de frutos no estágio médio 3 na coleta atual.
 *
 * @function validateMediumStage3
 * @param {IMediumStage3} mediumStage3PreviousVisit - Os dados do estágio médio 3 na coleta anterior.
 * @param {IMediumStage3} mediumStage3CurrentVisit - Os dados do estágio médio 3 na coleta atual.
 * @param {number} medium2ToMedium3 - A quantidade de frutos do estágio médio 2 para o estágio médio 3 na coleta anterior.
 * @returns {ICheckQuantityFunc} O resultado da validação, indicando se há erros na quantidade de frutos no estágio médio 3.
 */
function validateMediumStage3(
  mediumStage3PreviousVisit: IMediumStage3,
  mediumStage3CurrentVisit: IMediumStage3,
  medium2ToMedium3: number,
): ICheckQuantityFunc {
  const harvestedMediumStage3 = mediumStage3CurrentVisit.harvested;
  const lostFruits =
    mediumStage3CurrentVisit.loss +
    mediumStage3CurrentVisit.piece +
    mediumStage3CurrentVisit.rat +
    mediumStage3CurrentVisit.rotten +
    mediumStage3CurrentVisit.witchs_broom;
  const medium3ToAdult1 = mediumStage3PreviousVisit.total;
  const expectedTotalCurrentVisit =
    medium2ToMedium3 - harvestedMediumStage3 - lostFruits;

  if (expectedTotalCurrentVisit === mediumStage3CurrentVisit.total) {
    return {
      isError: false,
      quantity: medium3ToAdult1,
    };
  }

  return {
    isError: true,
    title: 'Realize a verificação de 84-105 novamente',
    message: 'Quantidade de frutos incorreta, ver coleta anterior',
  };
}

/**
 * Valida a quantidade de frutos no estágio adulto 1 na coleta atual.
 *
 * @function validateAdultsStage1
 * @param {IAdultStage1} adultsStage1PreviousVisit - Os dados do estágio adulto 1 na coleta anterior.
 * @param {IAdultStage1} adultsStage1CurrentVisit - Os dados do estágio adulto 1 na coleta atual.
 * @param {number} medium3ToAdult1 - A quantidade de frutos do estágio médio 3 para o estágio adulto 1 na coleta anterior.
 * @returns {ICheckQuantityFunc} O resultado da validação, indicando se há erros na quantidade de frutos no estágio adulto 1.
 */
function validateAdultsStage1(
  adultsStage1PreviousVisit: IAdultStage1,
  adultsStage1CurrentVisit: IAdultStage1,
  medium3ToAdult1: number,
): ICheckQuantityFunc {
  const harvestedAdultsStage1 = adultsStage1CurrentVisit.harvested;
  const lostFruits =
    adultsStage1CurrentVisit.loss +
    adultsStage1CurrentVisit.rat +
    adultsStage1CurrentVisit.rotten +
    adultsStage1CurrentVisit.witchs_broom;
  const adult1ToAdult2 = adultsStage1PreviousVisit.total;
  const expectedTotalCurrentVisit =
    medium3ToAdult1 - harvestedAdultsStage1 - lostFruits;

  if (expectedTotalCurrentVisit === adultsStage1CurrentVisit.total) {
    return {
      isError: false,
      quantity: adult1ToAdult2,
    };
  }

  return {
    isError: true,
    title: 'Realize a verificação de 105-126 novamente',
    message: 'Quantidade de frutos incorreta, ver coleta anterior',
  };
}

/**
 * Valida a quantidade de frutos no estágio adulto 2 na coleta atual.
 *
 * @function validateAdultsStage2
 * @param {IAdultStage2} adultsStage2PreviousVisit - Os dados do estágio adulto 2 na coleta anterior.
 * @param {IAdultStage2} adultsStage2CurrentVisit - Os dados do estágio adulto 2 na coleta atual.
 * @param {number} adult1ToAdult2 - A quantidade de frutos do estágio adulto 1 para o estágio adulto 2 na coleta anterior.
 * @returns {ICheckQuantityFunc} O resultado da validação, indicando se há erros na quantidade de frutos no estágio adulto 2.
 */
function validateAdultsStage2(
  adultsStage2PreviousVisit: IAdultStage2,
  adultsStage2CurrentVisit: IAdultStage2,
  adult1ToAdult2: number,
): ICheckQuantityFunc {
  const harvestedAdultsStage2 = adultsStage2CurrentVisit.harvested;
  const lostFruits =
    adultsStage2CurrentVisit.loss +
    adultsStage2CurrentVisit.rat +
    adultsStage2CurrentVisit.rotten +
    adultsStage2CurrentVisit.witchs_broom;
  const adult2ToMature1 = adultsStage2PreviousVisit.total;
  const expectedTotalCurrentVisit =
    adult1ToAdult2 - harvestedAdultsStage2 - lostFruits;

  if (expectedTotalCurrentVisit === adultsStage2CurrentVisit.total) {
    return {
      isError: false,
      quantity: adult2ToMature1,
    };
  }

  return {
    isError: true,
    title: 'Realize a verificação de 126-147 novamente',
    message: 'Quantidade de frutos incorreta, ver coleta anterior',
  };
}

/**
 * Valida a quantidade de frutos no estágio maduro 1 na coleta atual.
 *
 * @function validateMatureStage1
 * @param {IMatureStage1} matureStage1PreviousVisit - Os dados do estágio maduro 1 na coleta anterior.
 * @param {IMatureStage1} matureStage1CurrentVisit - Os dados do estágio maduro 1 na coleta atual.
 * @param {number} adult2ToMature1 - A quantidade de frutos do estágio adulto 2 para o estágio maduro 1 na coleta anterior.
 * @returns {ICheckQuantityFunc} O resultado da validação, indicando se há erros na quantidade de frutos no estágio maduro 1.
 */
function validateMatureStage1(
  matureStage1PreviousVisit: IMatureStage1,
  matureStage1CurrentVisit: IMatureStage1,
  adult2ToMature1: number,
): ICheckQuantityFunc {
  const harvestedMatureStage1 = matureStage1CurrentVisit.harvested;
  const lostFruits =
    matureStage1CurrentVisit.loss +
    matureStage1CurrentVisit.rat +
    matureStage1CurrentVisit.rotten +
    matureStage1CurrentVisit.witchs_broom;
  const mature1ToMature2 = matureStage1PreviousVisit.total;
  const expectedTotalCurrentVisit =
    adult2ToMature1 - harvestedMatureStage1 - lostFruits;

  if (expectedTotalCurrentVisit === matureStage1CurrentVisit.total) {
    return {
      isError: false,
      quantity: mature1ToMature2,
    };
  }

  return {
    isError: true,
    title: 'Realize a verificação de 147-168 novamente',
    message: 'Quantidade de frutos incorreta, ver coleta anterior',
  };
}

/**
 * Valida a quantidade de frutos no estágio maduro 2 na coleta atual.
 *
 * @function validateMatureStage2
 * @param {IMatureStage2} matureStage2PreviousVisit - Os dados do estágio maduro 2 na coleta anterior.
 * @param {IMatureStage2} matureStage2CurrentVisit - Os dados do estágio maduro 2 na coleta atual.
 * @param {number} mature1ToMature2 - A quantidade de frutos do estágio maduro 1 para o estágio maduro 2 na coleta anterior.
 * @returns {ICheckQuantityFunc} O resultado da validação, indicando se há erros na quantidade de frutos no estágio maduro 2.
 */
function validateMatureStage2(
  matureStage2PreviousVisit: IMatureStage2,
  matureStage2CurrentVisit: IMatureStage2,
  mature1ToMature2: number,
): ICheckQuantityFunc {
  const harvestedMatureStage2 = matureStage2CurrentVisit.harvested;
  const lostFruits =
    matureStage2CurrentVisit.loss +
    matureStage2CurrentVisit.rat +
    matureStage2CurrentVisit.rotten +
    matureStage2CurrentVisit.witchs_broom;
  const mature2ToMature3 = matureStage2PreviousVisit.total;
  const expectedTotalCurrentVisit =
    mature1ToMature2 - harvestedMatureStage2 - lostFruits;

  if (expectedTotalCurrentVisit === matureStage2CurrentVisit.total) {
    return {
      isError: false,
      quantity: mature2ToMature3,
    };
  }

  return {
    isError: true,
    title: 'Realize a verificação de 168-189 novamente',
    message: 'Quantidade de frutos incorreta, ver coleta anterior',
  };
}

/**
 * Valida a quantidade de frutos no estágio maduro 3 na coleta atual.
 *
 * @function validateMatureStage3
 * @param {IMatureStage3} matureStage3PreviousVisit - Os dados do estágio maduro 3 na coleta anterior.
 * @param {IMatureStage3} matureStage3CurrentVisit - Os dados do estágio maduro 3 na coleta atual.
 * @param {number} mature2ToMature3 - A quantidade de frutos do estágio maduro 2 para o estágio maduro 3 na coleta anterior.
 * @returns {ICheckQuantityFunc} O resultado da validação, indicando se há erros na quantidade de frutos no estágio maduro 3.
 */
function validateMatureStage3(
  matureStage3PreviousVisit: IMatureStage3,
  matureStage3CurrentVisit: IMatureStage3,
  mature2ToMature3: number,
): ICheckQuantityFunc {
  const harvestedMatureStage3 = matureStage3CurrentVisit.harvested;
  const lostFruits =
    matureStage3CurrentVisit.loss +
    matureStage3CurrentVisit.rat +
    matureStage3CurrentVisit.rotten +
    matureStage3CurrentVisit.witchs_broom;
  const mature3ToMature4 = matureStage3PreviousVisit.total;
  const expectedTotalCurrentVisit =
    mature2ToMature3 - harvestedMatureStage3 - lostFruits;

  if (expectedTotalCurrentVisit === matureStage3CurrentVisit.total) {
    return {
      isError: false,
      quantity: mature3ToMature4,
    };
  }

  return {
    isError: true,
    title: 'Realize a verificação de 189-210 novamente',
    message: 'Quantidade de frutos incorreta, ver coleta anterior',
  };
}

/**
 * Valida a quantidade de frutos no estágio maduro 4 na coleta atual.
 *
 * @function validateMatureStage4
 * @param {IMatureStage4} matureStage4CurrentVisit - Os dados do estágio maduro 4 na coleta atual.
 * @param {number} mature3ToMature4 - A quantidade de frutos do estágio maduro 3 para o estágio maduro 4 na coleta anterior.
 * @returns {ICheckQuantityFunc} O resultado da validação, indicando se há erros na quantidade de frutos no estágio maduro 4.
 */
function validateMatureStage4(
  matureStage4CurrentVisit: IMatureStage4,
  mature3ToMature4: number,
): ICheckQuantityFunc {
  const harvestedMatureStage4 = matureStage4CurrentVisit.harvested;
  const lostFruits =
    matureStage4CurrentVisit.loss +
    matureStage4CurrentVisit.rat +
    matureStage4CurrentVisit.rotten +
    matureStage4CurrentVisit.witchs_broom;
  const expectedTotalCurrentVisit =
    mature3ToMature4 - harvestedMatureStage4 - lostFruits;

  if (
    expectedTotalCurrentVisit === matureStage4CurrentVisit.total ||
    expectedTotalCurrentVisit < matureStage4CurrentVisit.total
  ) {
    return {
      isError: false,
    };
  }

  return {
    isError: true,
    title: 'Realize a verificação de 210 novamente',
    message: 'Quantidade de frutos incorreta, ver coleta anterior',
  };
}

/**
 * Valida a quantidade total de frutos em diferentes estágios da coleta e compara com a coleta anterior.
 *
 * @function validateTotalFruits
 * @param {ICollect} currentVisit - Os dados da coleta atual.
 * @param {ICollect} lastVisit - Os dados da coleta anterior.
 * @returns {ICheckQuantityFunc} O resultado da validação, indicando se há erros na quantidade de frutos.
 */
function validateTotalFruits(
  currentVisit: ICollect,
  lastVisit: ICollect,
): ICheckQuantityFunc {
  const {
    bobbin: currentVisitBilros,
    small: currentVisitSmalls,
    medium: currentVisitMedium,
    medium2: currentVisitMedium2,
    medium3: currentVisitMedium3,
    adult: currentVisitAdults,
    adult2: currentVisitAdults2,
    mature: currentVisitMature,
    mature2: currentVisitMature2,
    mature3: currentVisitMature3,
    mature4: currentVisitMature4,
  } = currentVisit;

  const {
    bobbin: previousVisitBilros,
    small: previousVisitSmalls,
    medium: previousVisitMedium,
    medium2: previousVisitMedium2,
    medium3: previousVisitMedium3,
    adult: previousVisitAdults,
    adult2: previousVisitAdults2,
    mature: previousVisitMature,
    mature2: previousVisitMature2,
    mature3: previousVisitMature3,
    mature4: previousVisitMature4,
  } = lastVisit;

  const totalBilros = previousVisitBilros.total;
  const totalSmalls =
    previousVisitSmalls.total -
    (currentVisitSmalls.loss +
      currentVisitSmalls.witchs_broom +
      currentVisitSmalls.piece);
  const totalMedium =
    previousVisitMedium.total -
    (currentVisitMedium.loss + currentVisitMedium.piece);
  const totalMediumStage2 =
    previousVisitMedium2.total -
    (currentVisitMedium2.loss +
      currentVisitMedium2.piece +
      currentVisitMedium2.witchs_broom +
      currentVisitMedium2.rotten +
      currentVisitMedium2.rat);
  const totalMediumStage3 =
    previousVisitMedium3.total -
    (currentVisitMedium3.harvested +
      currentVisitMedium3.loss +
      currentVisitMedium3.piece +
      currentVisitMedium3.witchs_broom +
      currentVisitMedium3.rotten +
      currentVisitMedium3.rat);
  const totalAdults =
    previousVisitAdults.total -
    (currentVisitAdults.harvested +
      currentVisitAdults.loss +
      currentVisitAdults.witchs_broom +
      currentVisitAdults.rotten +
      currentVisitAdults.rat);
  const totalAdultsStage2 =
    previousVisitAdults2.total -
    (currentVisitAdults2.harvested +
      currentVisitAdults2.loss +
      currentVisitAdults2.witchs_broom +
      currentVisitAdults2.rotten +
      currentVisitAdults2.rat);
  const totalMature =
    previousVisitMature.total -
    (currentVisitMature.harvested +
      currentVisitMature.loss +
      currentVisitMature.rat +
      currentVisitMature.rotten +
      currentVisitMature.witchs_broom);
  const totalMatureStage2 =
    previousVisitMature2.total -
    (currentVisitMature2.harvested +
      currentVisitMature2.loss +
      currentVisitMature2.rat +
      currentVisitMature2.rotten +
      currentVisitMature2.witchs_broom);
  const totalMatureStage3 =
    previousVisitMature3.total -
    (currentVisitMature3.harvested +
      currentVisitMature3.loss +
      currentVisitMature3.rat +
      currentVisitMature3.rotten +
      currentVisitMature3.witchs_broom);
  const totalMatureStage4 =
    previousVisitMature4.total -
    (currentVisitMature4.harvested +
      currentVisitMature4.loss +
      currentVisitMature4.rat +
      currentVisitMature4.rotten +
      currentVisitMature4.witchs_broom);

  const previousVisitSumTotal =
    totalBilros +
    totalSmalls +
    totalMedium +
    totalMediumStage2 +
    totalMediumStage3 +
    totalAdults +
    totalAdultsStage2 +
    totalMature +
    totalMatureStage2 +
    totalMatureStage3 +
    totalMatureStage4;

  const currentVisitSumTotal =
    currentVisitBilros.total +
    currentVisitSmalls.total +
    currentVisitMedium.total +
    currentVisitMedium2.total +
    currentVisitMedium3.total +
    currentVisitAdults.total +
    currentVisitAdults2.total +
    currentVisitMature.total +
    currentVisitMature2.total +
    currentVisitMature3.total +
    currentVisitMature4.total;

  if (
    currentVisitSumTotal - previousVisitSumTotal === 0 ||
    currentVisitSumTotal - previousVisitSumTotal > 0
  ) {
    return {
      isError: false,
    };
  }

  return {
    isError: true,
    title: 'Realize a verificação dos frutos novamente',
    message: 'Quantidade de frutos incorreta, ver coleta anterior',
  };
}

/**
 * Valida a quantidade de frutos colhidos em diferentes estágios da coleta e compara com coletas anteriores.
 *
 * @function validateCollect
 * @param {ICollect} collect - Os dados da coleta atual.
 * @param {ICollect} lastVisit - Os dados da coleta anterior.
 * @returns {ICheckQuantityFunc} O resultado da validação, incluindo informações sobre erros ou uma quantidade válida.
 */
function validateCollect(
  collect: ICollect,
  lastVisit: ICollect,
): ICheckQuantityFunc {
  const {
    bobbinPV: bilrosPVCurrentVisit,
    small: smallsCurrentVisit,
    medium: mediumCurrentVisit,
    medium2: medium2CurrentVisit,
    medium3: medium3CurrentVisit,
    adult: adultsCurrentVisit,
    adult2: adults2CurrentVisit,
    mature: matureCurrentVisit,
    mature2: mature2CurrentVisit,
    mature3: mature3CurrentVisit,
    mature4: mature4CurrentVisit,
  } = collect;

  const {
    bobbin: bilrosPreviousVisit,
    small: smallsPreviousVisit,
    medium: mediumPreviousVisit,
    medium2: medium2PreviousVisit,
    medium3: medium3PreviousVisit,
    adult: adultsPreviousVisit,
    adult2: adults2PreviousVisit,
    mature: maturePreviousVisit,
    mature2: mature2PreviousVisit,
    mature3: mature3PreviousVisit,
  } = lastVisit;

  const bilrosToSmall = bilrosPreviousVisit.total;

  // validations
  const validatedCollect = validateTotalFruits(collect, lastVisit);
  const validatedBilros = validateBilros(
    bilrosPreviousVisit,
    bilrosPVCurrentVisit,
  );
  const validatedSmalls = validateSmalls(
    smallsPreviousVisit,
    smallsCurrentVisit,
    bilrosToSmall,
  );
  const validatedMediumStage1 = validateMediumStage1(
    mediumPreviousVisit,
    mediumCurrentVisit,
    validatedSmalls.quantity ?? 0,
  );
  const validatedMediumStage2 = validateMediumStage2(
    medium2PreviousVisit,
    medium2CurrentVisit,
    validatedMediumStage1.quantity ?? 0,
  );
  const validatedMediumStage3 = validateMediumStage3(
    medium3PreviousVisit,
    medium3CurrentVisit,
    validatedMediumStage2.quantity ?? 0,
  );
  const validatedAdultsStage1 = !validatedMediumStage3.isError
    ? validateAdultsStage1(
        adultsPreviousVisit,
        adultsCurrentVisit,
        validatedMediumStage3.quantity ?? 0,
      )
    : validatedMediumStage3;
  const validatedAdultsStage2 = !validatedAdultsStage1.isError
    ? validateAdultsStage2(
        adults2PreviousVisit,
        adults2CurrentVisit,
        validatedAdultsStage1.quantity ?? 0,
      )
    : validatedAdultsStage1;
  const validatedMatureStage1 = !validatedAdultsStage2.isError
    ? validateMatureStage1(
        maturePreviousVisit,
        matureCurrentVisit,
        validatedAdultsStage2.quantity ?? 0,
      )
    : validatedAdultsStage2;
  const validatedMatureStage2 = !validatedMatureStage1.isError
    ? validateMatureStage2(
        mature2PreviousVisit,
        mature2CurrentVisit,
        validatedMatureStage1.quantity ?? 0,
      )
    : validatedMatureStage1;
  const validatedMatureStage3 = !validatedMatureStage2.isError
    ? validateMatureStage3(
        mature3PreviousVisit,
        mature3CurrentVisit,
        validatedMatureStage2.quantity ?? 0,
      )
    : validatedMatureStage2;
  const validatedMatureStage4 = !validatedMatureStage3.isError
    ? validateMatureStage4(
        mature4CurrentVisit,
        validatedMatureStage3.quantity ?? 0,
      )
    : validatedMatureStage3;

  const resultValidation: ICheckQuantityFunc | any = !validatedBilros.isError
    ? !validatedSmalls.isError
      ? !validatedMediumStage1.isError
        ? !validatedMediumStage2.isError
          ? !validatedMediumStage3.isError
            ? !validatedAdultsStage1.isError
              ? !validatedAdultsStage2.isError
                ? !validatedMatureStage1.isError
                  ? !validatedMatureStage2.isError
                    ? !validatedMatureStage3.isError
                      ? !validatedMatureStage4.isError
                        ? !validatedSmalls.isError
                        : validatedMatureStage4
                      : validatedMatureStage3
                    : validatedMatureStage2
                  : validatedMatureStage1
                : validatedAdultsStage2
              : validatedAdultsStage1
            : validatedMediumStage3
          : validatedMediumStage2
        : validatedMediumStage1
      : validatedSmalls
    : validatedBilros;

  return resultValidation.isError
    ? resultValidation
    : validatedCollect.isError
    ? validatedCollect
    : resultValidation;
}

export const validator = { validateCollect };
