import { jwtDecode } from 'jwt-decode';
import store from '@store/store';

function verifyToken(): boolean {
  const { accessToken } = store.getState().authReducer.auth;
  if (accessToken) {
    const decodedToken = jwtDecode<any>(accessToken);
    if (decodedToken.exp && decodedToken.exp < Date.now() / 1000) {
      return false;
    }
    return true;
  }

  return false;
}

export const auth = { verifyToken };
