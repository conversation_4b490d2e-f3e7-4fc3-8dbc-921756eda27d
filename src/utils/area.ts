import { AREA_INFO } from '@global/constants';
import { IAreaInfo } from '@global/types/visitInformation.types';

/**
 * Obtém informações da área com base em um critério personalizado.
 *
 * @param {Function} filterFunction - A função de filtro personalizada.
 * @returns {Array<any>} Um array de informações da área que atendem ao critério especificado.
 *
 *
 * Exemplos de uso:
 *
 * Obtém informações da área para áreas com IDs maiores que 3.
 *
 * const areaInfo1 = getAreaInfo(info => info.id > 3);
 *
 * Obtém informações da área para áreas com IDs menores que 4.
 *
 * const areaInfo2 = getAreaInfo(info => info.id < 4);
 */
function getAreaInfo(filterFunction: (info: any) => boolean): Array<IAreaInfo> {
  const areaInfo = AREA_INFO.filter(filterFunction);
  return areaInfo;
}

export const area = {
  getAreaInfo,
};
