/**
 * Verifica e adiciona um zero à esquerda a uma string se ela tiver apenas um caractere.
 *
 * @function checkZero
 * @param {string} value - A string a ser verificada.
 * @returns {string} A string original ou a string com um zero à esquerda.
 */
function checkZero(value: string): string {
  let _value: string = value;
  if (value.length === 1) {
    _value = `0${_value}`;
  }

  return _value;
}

/**
 * Retorna a data atual formatada no formato "DD/MM/AAAA".
 *
 * @function formatCurrentDate
 * @returns {string} A data atual formatada.
 */
function formatCurrentDate(formatHour: boolean = false): string {
  const date = new Date();
  const day = date.getDate().toString();
  const month = (date.getMonth() + 1).toString();

  const checkedDay = checkZero(day);
  const checkedMonth = checkZero(month);
  const checkedYear = date.getFullYear();

  if (formatHour) {
    const hour = date.getHours().toString();
    const minutes = date.getMinutes().toString();
    const seconds = date.getSeconds().toString();
    const checkedHour = checkZero(hour);
    const checkedMinutes = checkZero(minutes);
    const checkedSeconds = checkZero(seconds);

    const formatedDate = `${checkedYear}-${checkedMonth}-${checkedDay} ${checkedHour}:${checkedMinutes}:${checkedSeconds}`;
    return formatedDate;
  }

  const formatedDate = `${checkedDay}/${checkedMonth}/${checkedYear}`;
  return formatedDate;
}

/**
 * Converte uma data no formato CSV para o formato "DD/MM/AAAA".
 *
 * @function parseCSVDate
 * @param {string} date - A data no formato CSV a ser convertida.
 * @returns {string} A data convertida no formato "DD/MM/AAAA".
 */
function parseCSVDate(date: string): string {
  const _date = new Date(date.split(' ')[0]);
  const day = checkZero(_date.getDate().toString());
  const month = checkZero((_date.getMonth() + 1).toString());
  const year = _date.getFullYear();

  const formatedDate = `${day}/${month}/${year}`;

  return formatedDate;
}

/**
 * Formata uma data no formato DD/MM/YYYY.
 *
 * @function formatDate
 * @param {string} dateString - A string de data a ser formatada.
 * @returns {string} A data formatada no formato DD/MM/YYYY.
 */
function formatDate(dateString: string): string {
  const date = new Date(dateString);

  const day = date.getDate() + 1;
  const month = date.getMonth() + 1;
  const year = date.getFullYear();

  const formattedDay = checkZero(day.toString());
  const formattedMonth = checkZero(month.toString());

  const formattedDate = `${formattedDay}/${formattedMonth}/${year}`;

  return formattedDate;
}

export const time = { formatCurrentDate, parseCSVDate, formatDate };
