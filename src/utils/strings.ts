/**
 * Capitaliza a primeira letra de uma string.
 *
 * @function capitalize
 * @param {string} value - A string a ser capitalizada.
 * @returns {string} A string com a primeira letra em maiúscula.
 */
function capitalize(value: string): string {
  if (typeof value !== 'string') {
    return '';
  }

  const _value = value.toLowerCase();
  return _value.charAt(0).toUpperCase() + value.slice(1);
}

/**
 * Obtém o valor de um atributo como uma string, tratando casos de valores vazios ou nulos.
 *
 * @function getAttrValue
 * @param {null | string | number | Date | undefined} value - O valor a ser convertido para uma string.
 * @returns {string} O valor convertido para string ou '-' se for vazio ou nulo.
 */
function getAttrValue(
  value: null | string | number | Date | undefined,
): string {
  return !!value && value !== '' ? value.toString() : '-';
}

export const strings = { capitalize, getAttrValue };
