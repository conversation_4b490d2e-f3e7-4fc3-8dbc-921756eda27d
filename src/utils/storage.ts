import AsyncStorage from '@react-native-async-storage/async-storage';

/**
 * Armazena um valor no armazenamento local usando uma chave específica.
 *
 * @async
 * @function setData
 * @param {string} key - A chave sob a qual o valor será armazenado.
 * @param {any} value - O valor a ser armazenado.
 * @throws {Error} Lança um erro se houver um problema ao armazenar o valor.
 */
async function setData(key: string, value: any): Promise<void> {
  try {
    await AsyncStorage.setItem(key, value);
  } catch (error) {
    console.error('storage:setData: ', error);
  }
}

/**
 * Recupera um valor do armazenamento local com base em uma chave específica.
 *
 * @async
 * @function getData
 * @param {string} key - A chave pela qual o valor será recuperado.
 * @returns {Promise<string | null | undefined>} O valor recuperado ou null/undefined se não encontrado.
 * @throws {<PERSON>rror} Lança um erro se houver um problema ao recuperar o valor.
 */
async function getData(key: string): Promise<string | null | undefined> {
  try {
    const value = await AsyncStorage.getItem(key);
    return value;
  } catch (error) {
    console.error('storage:getData: ', error);
  }
}

/**
 * Remove um item do armazenamento local com base em sua chave.
 *
 * @async
 * @function removeData
 * @param {string} key - A chave do item a ser removido do armazenamento local.
 * @throws {Error} Lança um erro se houver um problema ao remover o item.
 */
async function removeData(key: string): Promise<void> {
  try {
    await AsyncStorage.removeItem(key);
  } catch (error) {
    console.error('storage:removeData: ', error);
  }
}

/**
 * Remove todos os itens do armazenamento local.
 *
 * @async
 * @function clearAllData
 * @throws {Error} Lança um erro se houver um problema ao limpar todos os itens do armazenamento local.
 */
async function clearAllData(): Promise<void> {
  try {
    await AsyncStorage.clear();
  } catch (error) {
    console.error('storage:clearAllData: ', error);
  }
}

export const storage = {
  setData,
  getData,
  removeData,
  clearAllData,
};
