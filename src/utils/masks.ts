/**
 * Formata um número de CPF adicionando pontos e traço (máscara).
 *
 * @function cpfMask
 * @param {string} cpf - O número de CPF a ser formatado.
 * @returns {string} O CPF formatado com a máscara, no formato XXX.XXX.XXX-XX.
 */
function cpfMask(cpf: string): string {
  return cpf
    .replace(/\D/g, '')
    .replace(/(\d{3})(\d)/, '$1.$2')
    .replace(/(\d{3})(\d)/, '$1.$2')
    .replace(/(\d{3})(\d{1,2})$/, '$1-$2');
}

export const masks = { cpfMask };
