import { Icons } from '@assets/icons';

type RootStackParamList = {
  Login: undefined;
  BottomTabStack: undefined;
  Properties: undefined;
  HomogeneousAreas: { title?: string; subtitle: string };
  OperationalUnits: { title?: string; subtitle: string };
  PracticeData: undefined;
  PracticeAreaData: undefined;
  SamplingPoints: { title?: string; subtitle: string };
  Trees: { title?: string; subtitle: string };
  Collect: { title?: string; subtitle?: string };
};

interface ITabBarIconProps {
  name: keyof typeof Icons;
  label: string;
  focused: boolean;
  size: number;
}

export type { RootStackParamList, ITabBarIconProps };
