import React from 'react';
import {
  CardStyleInterpolators,
  StackHeaderProps,
  createStackNavigator,
} from '@react-navigation/stack';
// Components
import { Header } from '@components';
import { translate } from '@global/i18n';
// Screens
import Properties from '@screens/Properties';
import HomogeneousAreas from '@screens/HomogeneousAreas';
import OperationalUnits from '@screens/OperationalUnits';
import SamplingPoints from '@screens/SamplingPoints';
import Trees from '@screens/Trees';
import PracticeData from '@screens/PracticeData';
import PracticeAreaData from '@screens/PracticeAreaData';
import Collect from '@screens/Collect';

const Stack = createStackNavigator();

const HomeStack: React.FC = () => {
  function _renderHeader(props: StackHeaderProps): React.ReactElement {
    return <Header {...props} />;
  }

  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: true,
        cardStyleInterpolator: CardStyleInterpolators.forHorizontalIOS,
      }}>
      <Stack.Screen
        name={'Properties'}
        component={Properties}
        options={{ header: _renderHeader, title: translate('properties') }}
      />
      <Stack.Screen
        name={'HomogeneousAreas'}
        component={HomogeneousAreas}
        options={{
          header: _renderHeader,
          title: translate('homogeneousAreas'),
        }}
      />
      <Stack.Screen
        name={'OperationalUnits'}
        component={OperationalUnits}
        options={{
          header: _renderHeader,
          title: translate('operationalUnits'),
        }}
      />
      <Stack.Screen
        name={'PracticeData'}
        component={PracticeData}
        options={{ header: _renderHeader, title: translate('practiceData') }}
      />
      <Stack.Screen
        name={'PracticeAreaData'}
        component={PracticeAreaData}
        options={{ header: _renderHeader, title: translate('practiceData') }}
      />
      <Stack.Screen
        name={'SamplingPoints'}
        component={SamplingPoints}
        options={{
          header: _renderHeader,
          title: translate('operationalUnits'),
        }}
      />
      <Stack.Screen
        name={'Trees'}
        component={Trees}
        options={{
          header: _renderHeader,
          title: translate('trees'),
        }}
      />
      <Stack.Screen
        name={'Collect'}
        component={Collect}
        options={{ header: _renderHeader }}
      />
    </Stack.Navigator>
  );
};

export default HomeStack;
