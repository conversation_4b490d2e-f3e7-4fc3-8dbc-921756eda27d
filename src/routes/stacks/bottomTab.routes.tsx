import React from 'react';
import {
  BottomTabBarButtonProps,
  createBottomTabNavigator,
} from '@react-navigation/bottom-tabs';
// Components
import { colors } from '@theme';
import { translate } from '@global/i18n';
import { ITabBarIconProps } from '@routes.types';
import { CustomTabBarButton, Icon, TabBarIcon } from '@components';
// Screens
import About from '@screens/About';
import Profile from '@screens/Profile';
import HomeStack from './home.routes';

import { styles } from '../routes.styles';

const BottomTabNavigator = createBottomTabNavigator();

const BottomTabStack: React.FC = () => {
  function _renderTabBarIcon(
    props: ITabBarIconProps,
  ): React.ReactNode | undefined {
    return <TabBarIcon {...props} />;
  }

  function _renderCustomTabBarIcon(props: {
    focused: boolean;
    size: number;
  }): React.ReactNode | undefined {
    const { size } = props;
    return <Icon name={'home'} size={size} color={colors.white} />;
  }

  function _renderCustomTabBarButton(
    props: BottomTabBarButtonProps,
  ): React.ReactNode | undefined {
    return <CustomTabBarButton {...props} />;
  }

  return (
    <BottomTabNavigator.Navigator
      initialRouteName="Home"
      screenOptions={{
        headerShown: false,
        tabBarShowLabel: false,
        tabBarStyle: [styles.tabBarStyle, styles.shadow],
        tabBarHideOnKeyboard: true,
      }}>
      <BottomTabNavigator.Screen
        name={'About'}
        component={About}
        options={{
          tabBarIcon: ({ focused, size }) =>
            _renderTabBarIcon({
              focused,
              size,
              name: 'about',
              label: translate('about'),
            }),
        }}
      />
      <BottomTabNavigator.Screen
        name={'Home'}
        component={HomeStack}
        options={{
          headerShown: false,
          tabBarIcon: _renderCustomTabBarIcon,
          tabBarButton: _renderCustomTabBarButton,
        }}
      />
      <BottomTabNavigator.Screen
        name={'Profile'}
        component={Profile}
        options={{
          tabBarIcon: ({ focused, size }) =>
            _renderTabBarIcon({
              focused,
              size,
              name: 'profile',
              label: translate('profile'),
            }),
        }}
      />
    </BottomTabNavigator.Navigator>
  );
};

export default BottomTabStack;
