import React, { useEffect, useRef } from 'react';
import {
  NavigationContainer,
  useNavigationContainerRef,
} from '@react-navigation/native';
import {
  CardStyleInterpolators,
  createStackNavigator,
} from '@react-navigation/stack';
import { auth } from '@utils';
import Login from '@screens/Login';
import { RootStackParamList } from '@routes.types';
import { useAuth } from '@store/slices/auth/useAuth';
import BottomTabStack from './stacks/bottomTab.routes';

const Stack = createStackNavigator();

const Routes: React.FC = () => {
  const { logout } = useAuth();
  const routeNameRef = useRef<string | undefined>();
  const navigationRef = useNavigationContainerRef<RootStackParamList>();
  const isAuthenticated = auth.verifyToken();

  function _onReady(): void {
    routeNameRef.current = navigationRef.getCurrentRoute()?.name;
  }

  function _onStateChange(): void {
    const previousRouteName = routeNameRef.current;
    const currentRouteName = navigationRef.getCurrentRoute()?.name as string;

    if (previousRouteName !== currentRouteName) {
      console.log(`${previousRouteName} -> ${currentRouteName}`);
      routeNameRef.current = currentRouteName;
    }
  }

  useEffect(() => {
    if (isAuthenticated) {
      navigationRef.current?.navigate('BottomTabStack');
    } else {
      logout();
      navigationRef.current?.navigate('Login');
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isAuthenticated]);

  /**
   * MainStack
   *  - LoginScreen
   *  - BottomTabStack
   *
   * BottomTabStack
   *  - HomeStack
   *  - AboutScreen
   *  - ProfileScreen
   *
   * HomeStack
   *  - PropertiesScreen
   *  - HomogeneousAreasScreen
   *  - OperationalUnitsScreen
   *  - SamplingPointsScreen
   */
  return (
    <NavigationContainer
      ref={navigationRef}
      onReady={_onReady}
      onStateChange={_onStateChange}>
      <Stack.Navigator
        screenOptions={{
          headerShown: false,
          cardStyleInterpolator: CardStyleInterpolators.forHorizontalIOS,
        }}>
        <Stack.Screen name={'Login'} component={Login} />
        <Stack.Screen name={'BottomTabStack'} component={BottomTabStack} />
      </Stack.Navigator>
    </NavigationContainer>
  );
};

export default Routes;
