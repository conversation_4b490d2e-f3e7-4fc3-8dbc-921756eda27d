import Realm, { ObjectSchema } from 'realm';

class MatureStage2 extends Realm.Object<MatureStage2> {
  static primaryKey: string;

  static schema: ObjectSchema = {
    name: 'MatureStage2',
    primaryKey: 'id',
    properties: {
      id: { type: 'int', indexed: true },
      treeVisit: {
        type: 'linkingObjects',
        objectType: 'TreeVisit',
        property: 'mature2',
      },
      total: 'int?',
      harvested: 'int?',
      rotten: 'int?',
      rat: 'int?',
      witchs_broom: 'int?',
      loss: 'int?',
      created_at: 'string',
    },
  };
}

export default MatureStage2;
