import Realm, { ObjectSchema } from 'realm';

class AdultStage2 extends Realm.Object<AdultStage2> {
  static primaryKey: string;
  name!: string;

  static schema: ObjectSchema = {
    name: 'AdultStage2',
    primaryKey: 'id',
    properties: {
      id: { type: 'int', indexed: true },
      treeVisit: {
        type: 'linkingObjects',
        objectType: 'TreeVisit',
        property: 'adult2',
      },
      total: 'int?',
      rotten: 'int?',
      rat: 'int?',
      witchs_broom: 'int?',
      loss: 'int?',
      harvested: 'int?',
      created_at: 'string',
    },
  };
}

export default AdultStage2;
