import Realm, { ObjectSchema } from 'realm';

class Small extends Realm.Object<Small> {
  static primaryKey: string;
  name!: string;

  static schema: ObjectSchema = {
    name: 'Small',
    primaryKey: 'id',
    properties: {
      id: 'int?',
      treeVisit: {
        type: 'linkingObjects',
        objectType: 'TreeVisit',
        property: 'small',
      },
      total: 'int?',
      loss: 'int?',
      piece: 'int?',
      witchs_broom: 'int?',
      created_at: 'string',
    },
  };
}

export default Small;
