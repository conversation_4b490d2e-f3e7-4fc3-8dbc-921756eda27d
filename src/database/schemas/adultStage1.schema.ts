import Realm, { ObjectSchema } from 'realm';

class AdultStage1 extends Realm.Object<AdultStage1> {
  static primaryKey: string;
  name!: string;

  static schema: ObjectSchema = {
    name: 'AdultStage1',
    primaryKey: 'id',
    properties: {
      id: { type: 'int', indexed: true },
      treeVisit: {
        type: 'linkingObjects',
        objectType: 'TreeVisit',
        property: 'adult',
      },
      total: 'int?',
      rotten: 'int?',
      rat: 'int?',
      witchs_broom: 'int?',
      loss: 'int?',
      harvested: 'int?',
      created_at: 'string',
    },
  };
}

export default AdultStage1;
