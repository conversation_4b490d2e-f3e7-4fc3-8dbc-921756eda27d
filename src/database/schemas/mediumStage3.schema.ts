import Realm, { ObjectSchema } from 'realm';

class MediumStage3 extends Realm.Object<MediumStage3> {
  static primaryKey: string;
  name!: string;

  static schema: ObjectSchema = {
    name: 'MediumStage3',
    primaryKey: 'id',
    properties: {
      id: 'int?',
      treeVisit: {
        type: 'linkingObjects',
        objectType: 'TreeVisit',
        property: 'medium3',
      },
      total: 'int?',
      harvested: 'int?',
      rotten: 'int?',
      rat: 'int?',
      witchs_broom: 'int?',
      loss: 'int?',
      piece: 'int?',
      created_at: 'string',
    },
  };
}

export default MediumStage3;
