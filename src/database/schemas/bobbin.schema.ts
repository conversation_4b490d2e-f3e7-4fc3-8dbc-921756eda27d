import Realm, { ObjectSchema } from 'realm';

class <PERSON><PERSON> extends Realm.Object<Bobbin> {
  static primaryKey: string;

  static schema: ObjectSchema = {
    name: '<PERSON><PERSON>',
    primaryKey: 'id',
    properties: {
      id: { type: 'int', indexed: true },
      treeVisit: {
        type: 'linkingObjects',
        objectType: 'TreeVisit',
        property: 'bobbin',
      },
      total: 'int?',
      created_at: 'string',
    },
  };
}

export default Bobbin;
