import Realm, { ObjectSchema } from 'realm';

class MediumStage2 extends Realm.Object<MediumStage2> {
  static primaryKey: string;
  name!: string;

  static schema: ObjectSchema = {
    name: 'MediumStage2',
    primaryKey: 'id',
    properties: {
      id: 'int?',
      treeVisit: {
        type: 'linkingObjects',
        objectType: 'TreeVisit',
        property: 'medium2',
      },
      total: 'int?',
      rotten: 'int?',
      rat: 'int?',
      witchs_broom: 'int?',
      loss: 'int?',
      piece: 'int?',
      created_at: 'string',
    },
  };
}

export default MediumStage2;
