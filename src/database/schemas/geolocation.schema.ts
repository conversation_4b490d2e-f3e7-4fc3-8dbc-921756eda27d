import Realm, { ObjectSchema } from 'realm';

class Geolocation extends Realm.Object<Geolocation> {
  static primaryKey: string;
  // name!: string;

  static schema: ObjectSchema = {
    name: 'Geolocation',
    primaryKey: 'id',
    properties: {
      id: { type: 'int', indexed: true },
      latitude: 'int?',
      longitude: 'int?',
      ratio: 'int?',
      created_at: 'string',
    },
  };
}

export default Geolocation;
