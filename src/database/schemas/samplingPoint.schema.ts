import Realm, { ObjectSchema } from 'realm';

class SamplingPoint extends Realm.Object<SamplingPoint> {
  static primaryKey: string;
  // name!: string;

  static schema: ObjectSchema = {
    name: 'SamplingPoint',
    properties: {
      id: 'int',
      strata: {
        type: 'linkingObjects',
        objectType: 'Strata',
        property: 'sampling_points',
      },
      ini_period: 'int?',
      label: 'string?',
      harvest: 'int?',
      year: 'int?',
      users_ids: 'UsersId[]',
      sampling_point_tree_range: 'SamplingPointTreeRange?',
      trees: 'Tree[]',
      total_trees: 'int?',
      created_at: 'string?',
      updated_at: 'string?',
      lastVisit: 'string?',
    },
  };
}

export default SamplingPoint;
