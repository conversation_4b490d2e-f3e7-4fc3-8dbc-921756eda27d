import Realm, { ObjectSchema } from 'realm';

class TreeVisit extends Realm.Object<TreeVisit> {
  static primaryKey: string;
  name!: string;

  static schema: ObjectSchema = {
    name: 'TreeVisit',
    primaryKey: 'id',
    properties: {
      id: 'int?',
      tree: {
        type: 'linkingObjects',
        objectType: 'Tree',
        property: 'tree_visits',
      },
      bobbin: 'Bobbin',
      small: 'Small',
      medium: 'MediumStage1',
      medium2: 'MediumStage2',
      medium3: 'MediumStage3',
      adult: 'AdultStage1',
      adult2: 'AdultStage2',
      mature: 'MatureStage1',
      mature2: 'MatureStage2',
      mature3: 'MatureStage3',
      mature4: 'MatureStage4',
      total_good: 'int?',
      total_harvested: 'int?',
      total_loss: 'int?',
      total_piece: 'int?',
      total_rat: 'int?',
      total_rotten: 'int?',
      total_wb: 'int?',
      visit_information: 'VisitInformation',
      created_at: 'string',
      date: 'string',
    },
  };
}

export default TreeVisit;
