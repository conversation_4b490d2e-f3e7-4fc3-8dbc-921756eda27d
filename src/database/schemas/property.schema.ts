import Realm, { ObjectSchema } from 'realm';

class Property extends Realm.Object<Property> {
  static primaryKey: string;
  // name!: string;

  static schema: ObjectSchema = {
    name: 'Property',
    properties: {
      id: 'int',
      name: 'string?',
      owner_name: 'string?',
      city: 'string?',
      uf: 'string?',
      description: 'string?',
      homogeneous_areas: 'HomogeneousArea[]',
      created_at: 'string',
    },
  };
}

export default Property;
