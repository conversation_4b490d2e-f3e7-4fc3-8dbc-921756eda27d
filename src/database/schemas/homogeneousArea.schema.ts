import Realm, { ObjectSchema } from 'realm';

class HomogeneousArea extends Realm.Object<HomogeneousArea> {
  static primaryKey: string;

  static schema: ObjectSchema = {
    name: 'HomogeneousArea',
    properties: {
      id: 'int',
      label: 'string?',
      property: {
        type: 'linkingObjects',
        objectType: 'Property',
        property: 'homogeneous_areas',
      },
      visit: 'VisitInformation',
      strata: 'Strata[]',
      user: 'User?',
      total_stratum: 'int?',
      created_at: 'string',
    },
  };
}

export default HomogeneousArea;
