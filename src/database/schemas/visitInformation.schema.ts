import Realm, { ObjectSchema } from 'realm';

class VisitInformation extends Realm.Object<VisitInformation> {
  static primaryKey: string;
  // name!: string;

  static schema: ObjectSchema = {
    name: 'VisitInformation',
    // primaryKey: 'id',
    properties: {
      // id: 'int?',
      treeVisit: {
        type: 'linkingObjects',
        objectType: 'TreeVisit',
        property: 'visit_information',
      },
      homogeneousArea: {
        type: 'linkingObjects',
        objectType: 'HomogeneousArea',
        property: 'visit',
      },
      date: 'string?',
      note: 'string?',
      flowering: 'int?',
      refoliation: 'int?',
      top: 'int?',
      pruned: 'int?',
      mowing: 'int?',
      weeding: 'int?',
      grated: 'int?',
      renewed: 'int?',
      fertilized: 'int?',
      pulverized: 'int?',
      unbounded: 'int?',
      wind: 'int?',
      brown_rot: 'int?',
      drought: 'int?',
      rain: 'int?',
      rat: 'int?',
      flood: 'int?',
      insect: 'int?',
      absence_of_shadow: 'int?',
      excess_shade: 'int?',
      created_at: 'string',
    },
  };
}

export default VisitInformation;
