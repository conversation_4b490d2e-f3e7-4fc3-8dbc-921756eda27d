import Realm, { ObjectSchema } from 'realm';

class MatureStage1 extends Realm.Object<MatureStage1> {
  static primaryKey: string;
  // name!: string;

  static schema: ObjectSchema = {
    name: 'MatureStage1',
    primaryKey: 'id',
    properties: {
      id: { type: 'int', indexed: true },
      treeVisit: {
        type: 'linkingObjects',
        objectType: 'TreeVisit',
        property: 'mature',
      },
      total: 'int?',
      harvested: 'int?',
      rotten: 'int?',
      rat: 'int?',
      witchs_broom: 'int?',
      loss: 'int?',
      created_at: 'string',
    },
  };
}

export default MatureStage1;
