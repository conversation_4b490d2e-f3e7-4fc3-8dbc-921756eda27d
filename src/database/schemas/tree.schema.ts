import Realm, { ObjectSchema } from 'realm';

class Tree extends Realm.Object<Tree> {
  static primaryKey: string;
  name!: string;

  static schema: ObjectSchema = {
    name: 'Tree',
    properties: {
      id: { type: 'int', indexed: true },
      label: 'string?',
      sampling_point_id: 'int?',
      tree_visits: 'TreeVisit[]',
      samplingPoint: {
        type: 'linkingObjects',
        objectType: 'SamplingPoint',
        property: 'trees',
      },
      rfid: 'Rfid?',
      alternative_label: 'string?',
      status: 'int?',
      created_at: 'string?',
    },
  };
}

export default Tree;
