import Realm, { ObjectSchema } from 'realm';

export default class Rfid extends Realm.Object<Rfid> {
  static primaryKey: string;
  rfid!: string;

  static schema: ObjectSchema = {
    name: 'Rfid',
    properties: {
      id: { type: 'int', indexed: true },
      code: 'string',
      tree: {
        type: 'linkingObjects',
        objectType: 'Tree',
        property: 'rfid',
      },
      created_at: 'string?',
      updated_at: 'string?',
    },
  };
}
