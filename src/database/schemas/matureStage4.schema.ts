import Realm, { ObjectSchema } from 'realm';

class MatureStage4 extends Realm.Object<MatureStage4> {
  static primaryKey: string;
  name!: string;

  static schema: ObjectSchema = {
    name: 'MatureStage4',
    primaryKey: 'id',
    properties: {
      id: 'int?',
      treeVisit: {
        type: 'linkingObjects',
        objectType: 'TreeVisit',
        property: 'mature4',
      },
      total: 'int?',
      harvested: 'int?',
      rotten: 'int?',
      rat: 'int?',
      witchs_broom: 'int?',
      loss: 'int?',
      created_at: 'string',
    },
  };
}

export default MatureStage4;
