import Realm, { ObjectSchema } from 'realm';

class MatureStage3 extends Realm.Object<MatureStage3> {
  static primaryKey: string;
  name!: string;

  static schema: ObjectSchema = {
    name: 'MatureStage3',
    primaryKey: 'id',
    properties: {
      id: 'int?',
      treeVisit: {
        type: 'linkingObjects',
        objectType: 'TreeVisit',
        property: 'mature3',
      },
      total: 'int?',
      harvested: 'int?',
      rotten: 'int?',
      rat: 'int?',
      witchs_broom: 'int?',
      loss: 'int?',
      created_at: 'string',
    },
  };
}

export default MatureStage3;
