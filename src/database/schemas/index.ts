import AdultStage1 from './adultStage1.schema';
import AdultStage2 from './adultStage2.schema';
import <PERSON>bin from './bobbin.schema';
import Geolocation from './geolocation.schema';
import HomogeneousArea from './homogeneousArea.schema';
import MatureStage1 from './matureStage1.schema';
import MatureStage2 from './matureStage2.schema';
import MatureStage3 from './matureStage3.schema';
import MatureStage4 from './matureStage4.schema';
import MediumStage1 from './mediumStage1.schema';
import MediumStage2 from './mediumStage2.schema';
import MediumStage3 from './mediumStage3.schema';
import Property from './property.schema';
import Rfid from './rfid.schema';
import SamplingPoint from './samplingPoint.schema';
import SamplingPointTreeRange from './samplingPointsTreeRange.schema';
import Small from './small.schema';
import Strata from './strata.schema';
import StratumTreeRange from './stratumTreeRange.schema';
import Tree from './tree.schema';
import TreeVisit from './treeVisit.schema';
import User from './user.schema';
import UsersId from './usersId.schema';
import VisitInformation from './visitInformation.schema';

const schemas = [
  AdultStage1,
  AdultStage2,
  Bobbin,
  Geolocation,
  HomogeneousArea,
  MatureStage1,
  MatureStage2,
  MatureStage3,
  MatureStage4,
  MediumStage1,
  MediumStage2,
  MediumStage3,
  Property,
  SamplingPoint,
  SamplingPointTreeRange,
  Small,
  Strata,
  StratumTreeRange,
  Tree,
  TreeVisit,
  User,
  UsersId,
  VisitInformation,
  Rfid,
];

export { schemas };
