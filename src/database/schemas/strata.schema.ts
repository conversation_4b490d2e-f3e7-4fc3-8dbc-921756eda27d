import Realm, { ObjectSchema } from 'realm';

class Strata extends Realm.Object<Strata> {
  static primaryKey: string;
  name!: string;

  static schema: ObjectSchema = {
    name: 'Strata',
    properties: {
      id: 'int',
      label: 'string?',
      homogeneous_areas: {
        type: 'linkingObjects',
        objectType: 'HomogeneousArea',
        property: 'strata',
      },
      stratum_tree_range: 'StratumTreeRange?',
      sampling_points: 'SamplingPoint[]',
      total_sampling_points: 'int?',
      created_at: 'string',
    },
  };
}

export default Strata;
