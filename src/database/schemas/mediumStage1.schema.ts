import Realm, { ObjectSchema } from 'realm';

class MediumStage1 extends Realm.Object<MediumStage1> {
  static primaryKey: string;
  name!: string;

  static schema: ObjectSchema = {
    name: 'MediumStage1',
    primaryKey: 'id',
    properties: {
      id: 'int?',
      treeVisit: {
        type: 'linkingObjects',
        objectType: 'TreeVisit',
        property: 'medium',
      },
      total: 'int?',
      loss: 'int?',
      piece: 'int?',
      created_at: 'string',
    },
  };
}

export default MediumStage1;
