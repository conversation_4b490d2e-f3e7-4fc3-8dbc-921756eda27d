import React from 'react';
import { Icon } from '@components';
import { colors } from '@theme';

import { IFloatingButtonProps } from './types';
import { FloatingButtonPressable } from './styles';
import { ActivityIndicator } from 'react-native';

const FloatingButton: React.FC<IFloatingButtonProps> = ({
  disabled = false,
  loading = false,
  onPress,
}) => {
  return (
    <FloatingButtonPressable disabled={disabled || loading} onPress={onPress}>
      {loading ? (
        <ActivityIndicator size={40} color={colors.white} />
      ) : (
        <Icon name="cpuChip" size={40} color={colors.snow} />
      )}
    </FloatingButtonPressable>
  );
};

export default FloatingButton;
