import React from 'react';
import { misc } from '@theme';
import { IProps } from './types';
import { TextInput } from './styles';

const NumberInput: React.FC<IProps> = ({
  value,
  editable,
  maxLength = 3,
  variant,
  ...restProps
}) => {
  return (
    <TextInput
      editable={editable}
      maxLength={maxLength}
      value={`${value ?? 0}`}
      variant={variant}
      style={[misc.effects.boxShadow, restProps.style]}
      {...restProps}
    />
  );
};

export default NumberInput;
