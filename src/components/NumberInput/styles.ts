import styled, { css } from 'styled-components/native';
import { colors, fonts } from '@theme';
import { INumberInputStyleProps } from './types';

export const TextInput = styled.TextInput.attrs(() => ({
  keyboardType: 'number-pad',
  placeholderTextColor: colors.gray,
  selectionColor: colors.black,
}))<INumberInputStyleProps>`
  background-color: #b2e9d0;
  width: 45px;
  height: 45px;

  font-weight: ${fonts.fontWeight.bold};
  font-size: ${fonts.fontSize.regular}px;
  text-align: center;
  color: ${colors.black};

  border-radius: 4px;

  ${({ variant }) =>
    variant === 'primary'
      ? css`
          background-color: ${colors.gray};
          color: ${colors.white};
        `
      : variant === 'secondary'
      ? css`
          background-color: #52b588;
          color: ${colors.white};
        `
      : null}
`;
