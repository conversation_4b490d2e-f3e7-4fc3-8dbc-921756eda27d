import React from 'react';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

import { Icon } from '@components';
import { RootStackParamList } from '@routes.types';
import { logout } from 'src/store/slices/auth/auth.slice';

import { IProps } from './types';
import {
  CenterContainer,
  Container,
  LeftButtonContainer,
  RightButtonContainer,
  SubTitle,
  Title,
} from './styles';

const Header: React.FC<IProps> = ({ ...props }) => {
  const navigation = useNavigation<StackNavigationProp<RootStackParamList>>();
  const { name, params } = useRoute<RouteProp<RootStackParamList>>();

  function handleLogout(): void {
    logout();
    navigation.navigate('Login');
  }

  function handleGoBack(): void {
    navigation.goBack();
  }

  return (
    <Container {...props}>
      {name !== 'Properties' && (
        <LeftButtonContainer onPress={handleGoBack}>
          <Icon name={'chevronLeft'} size={30} />
        </LeftButtonContainer>
      )}

      <CenterContainer>
        {params?.subtitle && <SubTitle>{params.subtitle}</SubTitle>}
        {(props.options.title || params?.title) && (
          <Title>{params?.title || props.options.title}</Title>
        )}
      </CenterContainer>

      <RightButtonContainer onPress={handleLogout}>
        <Icon name={'logout'} size={30} />
      </RightButtonContainer>
    </Container>
  );
};

export default React.memo(Header);
