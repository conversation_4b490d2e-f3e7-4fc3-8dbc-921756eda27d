import { Platform } from 'react-native';
import styled from 'styled-components/native';
import { Text } from '@components';
import { colors, fonts, spacing } from '@theme';

export const Container = styled.View`
  padding-top: ${Platform.OS === 'android'
    ? spacing.global.spacingMedium
    : spacing.global.spacingExtraLarge}px;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding-horizontal: ${spacing.global.spacingMedium}px;
  height: ${Platform.OS === 'android' ? '60' : '75'}px;
`;

export const LeftButtonContainer = styled.Pressable`
  flex: 1;
  max-width: 30px;
`;

export const CenterContainer = styled.View`
  flex: 1;
  align-items: center;
`;

export const Title = styled(Text)`
  text-align: center;
  font-family: Interstate;
  font-weight: 700;
  font-size: ${fonts.fontSize.regular}px;
  color: ${colors.black};
`;

export const SubTitle = styled(Text)`
  text-align: center;
  font-family: Interstate;
  font-weight: 700;
  font-size: ${fonts.fontSize.regular}px;
  color: ${colors.black};
`;

export const Image = styled.Image`
  resize-mode: contain;
  max-height: 50px;
  width: 75px;
`;

export const RightButtonContainer = styled.Pressable`
  flex: 1;
  max-width: 30px;
  align-items: flex-end;
`;
