import React from 'react';
import { Icon } from '@components';
import { colors } from '@theme';
import { IProps } from './types';
import {
  Container,
  IconContainer,
  RightContainer,
  RightLabel,
  Title,
} from './styles';

const ListItem: React.FC<IProps> = ({
  title,
  rightComponent,
  color,
  backgroundColor,
  onPress,
  ...restProps
}) => {
  return (
    <Container
      backgroundColor={backgroundColor}
      onPress={onPress}
      {...restProps}>
      <Title color={color}>{title}</Title>
      {rightComponent && (
        <RightContainer>
          <RightLabel color={rightComponent.color}>
            {rightComponent.label}
          </RightLabel>
        </RightContainer>
      )}
      <IconContainer>
        <Icon name="chevronRight" size={20} color={color || colors.white} />
      </IconContainer>
    </Container>
  );
};

export default ListItem;
