import styled from 'styled-components/native';
import { colors, spacing } from '@theme';
import { Text } from '@components';
import { IListItemStyleProps, ILabelStyleProps } from './types';

export const Container = styled.Pressable<IListItemStyleProps>`
  flex-direction: row;
  background-color: ${({ backgroundColor }) =>
    backgroundColor ?? colors.deepPetrolBlue};
  padding: 15px 10px;
  border-radius: 5px;
  margin: 5px 10px;
  align-items: center;
`;

export const RightContainer = styled.View`
  flex: 1;
  align-items: flex-end;
  justify-content: center;
  padding-horizontal: ${spacing.global.spacingSmall}px;
`;

export const RightLabel = styled(Text)<ILabelStyleProps>`
  color: ${({ color }) => color ?? colors.primary};
`;

export const Title = styled(Text)<IListItemStyleProps>`
  flex: 1;
  color: ${({ color }) => color ?? colors.white};
`;

export const IconContainer = styled.View``;
