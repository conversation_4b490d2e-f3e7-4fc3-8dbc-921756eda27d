import { PressableProps } from 'react-native';

interface IProps extends PressableProps {
  title: string;
  backgroundColor?: string;
  color?: string;
  rightComponent?: {
    label: string;
    color: string;
  };
}

interface IListItemStyleProps
  extends Pick<IProps, 'backgroundColor' | 'color'> {}

interface ILabelStyleProps {
  color: string;
}

export type { IProps, IListItemStyleProps, ILabelStyleProps };
