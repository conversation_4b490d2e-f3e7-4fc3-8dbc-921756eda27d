import React from 'react';
import { ActivityIndicator } from 'react-native';

import { colors } from '@theme';

import { IProps } from './types';
import { Container, Title } from './styles';

const Button: React.FC<IProps> = ({
  title,
  loading,
  onPress,
  ...restProps
}) => {
  return (
    <Container onPress={onPress} {...restProps}>
      {loading ? (
        <ActivityIndicator color={colors.white} />
      ) : (
        <Title>{title}</Title>
      )}
    </Container>
  );
};

export default Button;
