import styled from 'styled-components/native';
import { colors, fonts, spacing } from '@theme';
import { Text } from '@components';

export const Container = styled.Pressable`
  height: ${spacing.button.height}px;
  margin-vertical: ${spacing.button.marginVertical}px;
  padding-horizontal: ${spacing.button.paddingHorizontal}px;
  padding-vertical: ${spacing.button.paddingVertical}px;
  border-radius: ${spacing.button.borderRadius}px;
  justify-content: center;
  align-items: center;
  background-color: ${colors.secondary};
`;

export const Title = styled(Text)`
  text-align: center;
  font-weight: ${fonts.fontWeight.bold};
  color: ${colors.white};
  font-size: ${fonts.fontSize.regular}px;
`;
