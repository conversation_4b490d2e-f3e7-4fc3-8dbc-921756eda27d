import { TouchableOpacityProps } from 'react-native';

interface IProps {
  value: boolean;
  label?: string;
  handleChange: () => void;
  disabled?: boolean;
  iconColor?: string;
  labelColor?: string;
  borderColor?: string;
  backgroundColor?: string;
}

interface ICheckBoxButtonStyleProps
  extends TouchableOpacityProps,
    Pick<IProps, 'value' | 'borderColor' | 'backgroundColor'> {}

interface ICheckBoxLabelStyleProps extends Pick<IProps, 'labelColor'> {}

export type { IProps, ICheckBoxButtonStyleProps, ICheckBoxLabelStyleProps };
