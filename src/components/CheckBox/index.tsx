import React from 'react';
import { Icon } from '@components';

import { IProps } from './types';
import { CheckBoxButton, Container, LabelText } from './styles';

const CheckBox: React.FC<IProps> = ({
  value,
  label,
  handleChange,
  disabled = false,
  iconColor,
  labelColor,
  borderColor,
  backgroundColor,
}) => {
  return (
    <Container>
      <CheckBoxButton
        value={value}
        disabled={disabled}
        activeOpacity={1}
        onPress={handleChange}
        borderColor={borderColor}
        backgroundColor={backgroundColor}>
        {value && (
          <Icon name={'checkSolid'} size={20} color={iconColor ?? 'white'} />
        )}
      </CheckBoxButton>
      {label && <LabelText labelColor={labelColor}>{label}</LabelText>}
    </Container>
  );
};

export default CheckBox;
