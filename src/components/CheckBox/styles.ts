import styled from 'styled-components/native';
import { Text } from '@components';
import { colors, spacing } from '@theme';
import { ICheckBoxButtonStyleProps, ICheckBoxLabelStyleProps } from './types';

const Container = styled.View`
  flex-direction: row;
  gap: ${spacing.global.gap}px;
  align-items: center;
`;

const CheckBoxButton = styled.Pressable<ICheckBoxButtonStyleProps>`
  justify-content: center;
  align-items: center;
  width: 24px;
  height: 24px;
  border-radius: 5px;
  border-color: ${({ borderColor }) => borderColor ?? colors.secondary};
  border-width: 2px;
  background-color: ${({ value, backgroundColor }) =>
    value ? backgroundColor ?? colors.secondary : colors.transparent};
`;

const LabelText = styled(Text)<ICheckBoxLabelStyleProps>`
  font-family: Interstate;
  color: ${({ labelColor }) => labelColor ?? colors.secondary};
`;

export { Container, CheckBoxButton, LabelText };
