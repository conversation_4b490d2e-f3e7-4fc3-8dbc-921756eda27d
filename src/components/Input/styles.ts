import { colors, fonts, spacing } from '@theme';
import styled from 'styled-components/native';

export const Wrapper = styled.View`
  margin-bottom: 15px;
  gap: ${spacing.global.gap}px;
`;

export const Container = styled.View`
  background-color: ${colors.grayLighter};
  border-radius: ${spacing.input.bordeRadius}px;
  padding: ${spacing.input.padding}px;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
`;

export const TextInput = styled.TextInput`
  flex: 1;
  color: ${colors.black};
  font-size: ${fonts.fontSize.small}px;
  padding-vertical: 5px;
`;
