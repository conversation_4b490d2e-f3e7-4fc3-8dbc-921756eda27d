import React from 'react';
import { Pressable } from 'react-native';

import { colors } from '@theme';
import { Icon } from '@components';

import { IProps } from './types';
import { Container, TextInput, Wrapper } from './styles';

const Input: React.FC<IProps> = ({
  placeholder,
  value,
  secureTextEntry,
  onChangeText,
  textContentType,
  handleSecureTextEntryChange,
  ...restProps
}) => {
  return (
    <Wrapper>
      <Container>
        <TextInput
          value={value}
          onChangeText={onChangeText}
          placeholder={placeholder}
          placeholderTextColor={colors.gray}
          secureTextEntry={secureTextEntry}
          autoCorrect={false}
          autoCapitalize={'none'}
          autoComplete={'off'}
          {...restProps}
        />

        {textContentType === 'password' && (
          <Pressable onPress={handleSecureTextEntryChange}>
            <Icon name={secureTextEntry ? 'eye' : 'eyeOff'} size={25} />
          </Pressable>
        )}
      </Container>
    </Wrapper>
  );
};

export default Input;
