import React from 'react';
import { IProps } from './types';
import {
  AlertValidationContainer,
  AlertValidationContent,
  FooterButton,
  FooterButtonLabel,
  FooterContainer,
  IconContainer,
  Message,
  MessageContainer,
  Subtitle,
  SubtitleContainer,
  Title,
  TitleContainer,
} from './styles';
import { Modal } from 'react-native';
import Icon from '../Icon';
import { colors } from '@theme';

const AlertValidation: React.FC<IProps> = ({
  visible,
  title,
  subtitle,
  message,
  buttonLabel,
  secondaryButtonLabel,
  onSecondaryButtonPress,
  onClose,
}) => {
  return (
    <Modal
      transparent
      animationType="fade"
      visible={visible}
      onRequestClose={onClose}>
      <AlertValidationContainer>
        <AlertValidationContent>
          <IconContainer>
            <Icon name="warning" size={50} color={colors.statusRed} />
          </IconContainer>
          <TitleContainer>
            <Title>{title}</Title>
          </TitleContainer>

          {subtitle && (
            <SubtitleContainer>
              <Subtitle>{subtitle}</Subtitle>
            </SubtitleContainer>
          )}

          {message && (
            <MessageContainer>
              <Message>{message}</Message>
            </MessageContainer>
          )}

          <FooterContainer>
            <FooterButton onPress={onClose}>
              <FooterButtonLabel>{buttonLabel || 'OK'}</FooterButtonLabel>
            </FooterButton>
            {secondaryButtonLabel && onSecondaryButtonPress && (
              <FooterButton onPress={onSecondaryButtonPress}>
                <FooterButtonLabel>{secondaryButtonLabel}</FooterButtonLabel>
              </FooterButton>
            )}
          </FooterContainer>
        </AlertValidationContent>
      </AlertValidationContainer>
    </Modal>
  );
};

export default AlertValidation;
