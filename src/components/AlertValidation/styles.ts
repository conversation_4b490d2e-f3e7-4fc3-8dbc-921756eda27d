import styled from 'styled-components/native';
import { Text } from '@components';
import { colors, fonts, spacing } from '@theme';

export const AlertValidationContainer = styled.View`
  flex: 1;
  justify-content: center;
  background-color: #11111199;
`;

export const AlertValidationContent = styled.View`
  background-color: #fff;
  margin: 20px;
  padding: 20px;
  border-radius: 10px;
`;

export const IconContainer = styled.View`
  align-items: center;
  margint-top: 20px;
  margin-bottom: 18px;
`;

export const TitleContainer = styled.View`
  align-items: center;
  margin-bottom: 4px;
`;

export const Title = styled(Text)`
  font-size: ${fonts.fontSize.regular}px;
  font-weight: bold;
`;

export const SubtitleContainer = styled.View`
  align-items: center;
  margin-bottom: 16px;
`;

export const Subtitle = styled(Text)`
  color: ${colors.gray};
  text-align: center;
`;

export const MessageContainer = styled.View`
  margin-bottom: 20px;
`;

export const Message = styled(Text)`
  color: ${colors.gray};
  text-align: center;
`;

export const FooterContainer = styled.View`
  justify-content: center;
  align-items: flex-end;
  gap: ${spacing.global.gap}px;
`;

export const FooterButton = styled.Pressable`
  width: 100%;
  padding-vertical: ${spacing.global.spacingSmall}px;
  padding-horizontal: ${spacing.global.spacingMedium}px;
  background-color: ${colors.primary};
  border-radius: 4px;
`;

export const FooterButtonLabel = styled(Text)`
  color: ${colors.white};
  font-weight: 600;
  text-align: center;
`;
