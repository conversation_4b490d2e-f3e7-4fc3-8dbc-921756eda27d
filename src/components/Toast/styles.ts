import { StyleSheet } from 'react-native';
import { styled } from 'styled-components/native';
import { colors, spacing } from '@theme';

const styles = StyleSheet.create({
  toastContainer: {
    flex: 1,
    gap: 5,
    position: 'absolute',
    alignSelf: 'center',
    borderRadius: 10,
    zIndex: 9999999,
    width: '95%',
  },
  toastText: {
    fontSize: 12,
    color: colors.white,
    fontWeight: 'bold',
  },
  icon: {
    fontSize: 18,
    color: colors.white,
  },
});

const TouchableContainer = styled.TouchableOpacity`
  flex-direction: row;
  padding: 15px;
  align-items: center;
  gap: ${spacing.global.gap}px;
`;

export { styles, TouchableContainer };
