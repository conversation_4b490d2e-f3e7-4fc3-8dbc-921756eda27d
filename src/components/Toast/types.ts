import { StyleProp, ViewStyle } from 'react-native';
import { IToast } from '@context/ToastContext/types';
import { Icons } from '@assets/icons';

interface IProps {
  toast: IToast;
  style?: StyleProp<ViewStyle>;
}

interface IToastTypeAttr {
  iconName: keyof typeof Icons;
  backgroundColor: string;
}

interface IToastTypesAttrs {
  success: IToastTypeAttr;
  error: IToastTypeAttr;
  warning: IToastTypeAttr;
}

export type { IProps, IToastTypesAttrs };
