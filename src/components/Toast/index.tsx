import React, { useEffect, useMemo } from 'react';
import { Animated, Easing } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { colors } from '@theme';
import { Icon, Text } from '@components';
import { useToast } from '@context/ToastContext';
import { IProps, IToastTypesAttrs } from './types';
import { TouchableContainer, styles } from './styles';

const toastTypes: IToastTypesAttrs = {
  success: {
    iconName: 'check',
    backgroundColor: colors.statusGreen,
  },
  error: {
    iconName: 'error',
    backgroundColor: colors.statusRed,
  },
  warning: {
    iconName: 'warning',
    backgroundColor: colors.statusYellow,
  },
};

const Toast: React.FC<IProps> = ({ style }) => {
  const insets = useSafeAreaInsets();
  const { toast, hide } = useToast();
  const fadeAnim = useMemo(() => new Animated.Value(0), []);
  const { visible, type, message } = toast;
  const { iconName, backgroundColor } = toastTypes[type];

  useEffect(() => {
    function hideToast(): void {
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start(() => {
        hide();
      });
    }

    if (visible) {
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        easing: Easing.ease,
        useNativeDriver: true,
      }).start();
    }

    const timer = setTimeout(() => {
      hideToast();
    }, 3000);

    return () => {
      clearTimeout(timer);
    };
  }, [visible, fadeAnim, hide]);

  return (
    <>
      {visible && (
        <Animated.View
          style={[
            styles.toastContainer,
            style,
            { opacity: fadeAnim, backgroundColor, top: insets.top + 15 },
          ]}>
          <TouchableContainer activeOpacity={1} onPress={hide}>
            <Icon name={iconName} color={'white'} size={30} />
            <Text style={styles.toastText}>{message}</Text>
          </TouchableContainer>
        </Animated.View>
      )}
    </>
  );
};

export default Toast;
