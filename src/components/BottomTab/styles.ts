import { colors, fonts } from '@theme';
import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
  tabBarLabel: {
    fontSize: fonts.fontSize.xsmall,
  },
  tabBarIconContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  shadow: {
    shadowColor: colors.dark,
    shadowOffset: {
      width: 0,
      height: 10,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.5,
    elevation: 5,
  },
  tabBarStyle: {
    position: 'absolute',
    bottom: 20,
    left: 20,
    right: 20,
    height: 60,
    borderRadius: 15,
    backgroundColor: colors.snow,
  },
  customTabBarButton: {
    top: -30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  customTabBarButtonContainer: {
    width: 60,
    height: 60,
    borderRadius: 50,
    backgroundColor: colors.deepPetrolBlue,
  },
});

export { styles };
