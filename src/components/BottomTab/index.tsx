import React from 'react';
import { Pressable, View } from 'react-native';
import { BottomTabBarButtonProps } from '@react-navigation/bottom-tabs';

import { colors } from '@theme';
import { Icon, Text } from '@components';

import { styles } from './styles';
import { ITabBarIconProps } from './types';

const TabBarIcon: React.FC<ITabBarIconProps> = ({
  name,
  label,
  size,
  focused,
}) => {
  return (
    <View style={styles.tabBarIconContainer}>
      <Icon
        name={name}
        size={size}
        color={focused ? colors.deepPetrolBlue : colors.gray}
      />
      <Text
        style={[
          styles.tabBarLabel,
          { color: focused ? colors.deepPetrolBlue : colors.gray },
        ]}>
        {label}
      </Text>
    </View>
  );
};

const CustomTabBarButton: React.FC<BottomTabBarButtonProps> = ({
  children,
  onPress,
}): React.ReactNode => {
  return (
    <Pressable
      onPress={onPress}
      style={[styles.customTabBarButton, styles.shadow]}>
      <View style={styles.customTabBarButtonContainer}>{children}</View>
    </Pressable>
  );
};

export { CustomTabBarButton, TabBarIcon };
