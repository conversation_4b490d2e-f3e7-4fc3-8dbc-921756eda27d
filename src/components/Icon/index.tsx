import React from 'react';
import { View } from 'react-native';
import { Icons } from '@assets/icons';
import { colors } from '@theme';
import { IProps } from './types';

const Icon: React.FC<IProps> = ({ name, color, size = 50, ...props }) => {
  const Component = Icons[name];
  const selectedColor = color ? color : colors.black;

  return (
    <View>
      <Component
        fill={selectedColor}
        color={selectedColor}
        height={size}
        width={size}
        {...props}
      />
    </View>
  );
};

export default Icon;
