import React, {
  ReactNode,
  createContext,
  useCallback,
  useContext,
  useState,
} from 'react';
import { Toast } from '@components';
import { IToast, IToastContext } from './types';

const ToastContext = createContext<IToastContext | undefined>(undefined);

interface ToastProviderProps {
  children: ReactNode;
}

const ToastProvider: React.FC<ToastProviderProps> = ({ children }) => {
  const [toast, setToast] = useState<IToast>({
    type: 'success',
    message: '',
    visible: false,
  });

  const show = useCallback((values: IToast) => {
    setToast({ ...toast, visible: true, ...values });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const hide = useCallback(() => {
    setToast({ ...toast, visible: false });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <ToastContext.Provider value={{ toast, show, hide }}>
      {children}
      <Toast toast={toast} />
    </ToastContext.Provider>
  );
};

const useToast = (): IToastContext => {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
};

export { ToastProvider, useToast };
