import React from 'react';
import * as Sentry from '@sentry/react-native';
import { Provider } from 'react-redux';
import { PersistGate } from 'redux-persist/integration/react';
import { SafeAreaProvider } from 'react-native-safe-area-context';

import Routes from '@routes';
import { SENTRY_DSN } from '@env';
import store, { persistor } from '@store/store';
import { ToastProvider } from '@context/ToastContext';

Sentry.init({
  dsn: SENTRY_DSN,
  tracesSampleRate: 1.0,
});

const App: React.FC = () => {
  return (
    <Provider store={store}>
      <PersistGate persistor={persistor}>
        <SafeAreaProvider>
          <ToastProvider>
            <Routes />
          </ToastProvider>
        </SafeAreaProvider>
      </PersistGate>
    </Provider>
  );
};

export default Sentry.wrap(App);
