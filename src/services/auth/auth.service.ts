import { api } from '@services/server';
import { IAuthLogin } from '@global/types/auth.types';
import { IDefaultResponse } from '@services/server.types';
import { ILoginResponse, IUserResponse } from '@global/types/api.types';

/**
 * Realiza uma solicitação de login com email e senha fornecidos.
 *
 * @async
 * @function login
 * @param {IAuthLogin} authData - Um objeto contendo as informações de login.
 * @param {string} authData.email - O endereço de email para autenticação.
 * @param {string} authData.password - A senha para autenticação.
 * @returns {Promise<IDefaultResponse<ILoginResponse>>} Uma promessa que resolve com a resposta da solicitação de login.
 */
async function login({
  email,
  password,
}: IAuthLogin): Promise<IDefaultResponse<ILoginResponse>> {
  const response = await api.post('/login', { email, password });
  return response;
}

/**
 * Recupera informações do usuário autenticado.
 *
 * @async
 * @function me
 * @returns {Promise<IDefaultResponse<IUserResponse>>} Uma promessa que resolve com as informações do usuário autenticado.
 */
async function me(): Promise<IDefaultResponse<IUserResponse>> {
  const response = await api.get('/me');
  return response;
}

export const authService = { login, me };
