import { api } from '@services/server';
import store from 'src/store/store';
import { IDefaultResponse } from '@services/server.types';
import { ICollect, ICollectData } from '@global/types/collect.types';
import { IVisitInformation } from '@global/types/visitInformation.types';
import { resetCollect } from '@store/slices/collect/collect.slice';
import { resetPracticeCollect } from '@store/slices/practice/practice.slice';

/**
 * Recupera dados de coleta da aplicação.
 *
 * @async
 * @function getCollectData
 * @returns {Promise<ICollect>} Uma promessa que resolve com os dados de coleta da aplicação.
 */
async function getCollectData(): Promise<IDefaultResponse<Array<ICollect[]>>> {
  const response = await api.get('/app');
  return response;
}

/**
 * Envia os dados de coleta para a API.
 *
 * @async
 * @function sendCollectData
 * @returns {Promise<IDefaultResponse<ICollect>>} Uma promessa que resolve com a resposta da solicitação de envio dos dados de coleta.
 */
async function sendCollectData(): Promise<IDefaultResponse<ICollect>> {
  const _collect: ICollectData[] = [];
  store.getState().collectReducer.collect.map(c => {
    const totalSamplingPoints = c.sampling_points.length;

    let filteredSamplingPoints: typeof c = {
      sampling_points: [],
      stratum_id: undefined,
    };

    for (let i = 0; i < totalSamplingPoints; i++) {
      if (c.sampling_points[i].trees.length > 0) {
        filteredSamplingPoints.sampling_points.push(c.sampling_points[i]);
      }
      if (filteredSamplingPoints.sampling_points.length === 1) {
        filteredSamplingPoints.stratum_id = c.stratum_id;
      }
      if (filteredSamplingPoints.sampling_points.length > 0) {
        _collect.push(filteredSamplingPoints);
      }

      filteredSamplingPoints = {
        sampling_points: [],
        stratum_id: undefined,
      };
    }
  });
  const response = await api.post('/app', { data: _collect });
  store.dispatch(resetCollect());
  return response;
}

/**
 * Envia os dados de coleta de prática para a API.
 *
 * @async
 * @function sendPracticeCollectData
 * @returns {Promise<IDefaultResponse<IVisitInformation>>} Uma promessa que resolve com a resposta da solicitação de envio dos dados de coleta de prática.
 */
async function sendPracticeCollectData(): Promise<
  IDefaultResponse<IVisitInformation>
> {
  const practice = store.getState().practiceReducer.practice;
  const response = await api.post('/practice', { data: practice });
  store.dispatch(resetPracticeCollect());
  return response;
}

export const collectService = {
  getCollectData,
  sendCollectData,
  sendPracticeCollectData,
};
