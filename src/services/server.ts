import axios from 'axios';
import { API_HOST } from '@env';
import store from '@store/store';

const api = axios.create({
  baseURL: API_HOST,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

api.interceptors.request.use(function (request) {
  const { accessToken } = store.getState().authReducer.auth;
  if (accessToken && request.url !== '/auth/login') {
    request.headers.Authorization = `Bearer ${accessToken}`;
  }
  return request;
});

export { api };
