import Realm from 'realm';
import { realm } from '@database/database';
import { collectService } from '@services/collect/collect.service';
import { ICollect } from '@global/types/collect.types';

async function syncData(): Promise<void> {
  try {
    const { data } = await collectService.getCollectData();
    realm.write(() => {
      realm.deleteAll();
      const entities = ['Property', 'HomogeneousArea', 'Strata'];
      entities.forEach((entity, i) => {
        syncEntityData(realm, entity, data[i]);
      });
    });
  } catch (error) {
    console.error(error);
  }
}

function syncEntityData(
  realmInstance: Realm,
  entity: string,
  data: ICollect[],
): void {
  try {
    data.forEach(item => {
      realmInstance.create(entity, item, Realm.UpdateMode.Modified);
    });
  } catch (error) {
    console.error(error);
  }
}

export const realmService = { syncData };
