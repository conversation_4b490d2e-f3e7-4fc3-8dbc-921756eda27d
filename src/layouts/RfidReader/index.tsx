import React, { useEffect, useRef, useState } from 'react';
import {
  ActivityIndicator,
  Modal,
  NativeEventEmitter,
  NativeModules,
  TextInput,
} from 'react-native';
import Snackbar from 'react-native-snackbar';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

import { colors } from '@theme';
import { realm } from '@database/database';
import { FloatingButton } from '@components';
import { RootStackParamList } from '@routes.types';
import { useInfo } from '@store/slices/info/useInfo';
import { COMPATIBLE_VENDOR_IDS } from '@global/constants';
import { ISamplingPoint } from '@global/types/samplingPoint.types';

import { IRfidReaderLayoutProps } from './types';
import {
  ModalContainer,
  ModalContent,
  ModalText,
  RfidReaderContainer,
  styles,
} from './styles';

const { RfidModule } = NativeModules;
const rfidEmitter = new NativeEventEmitter(RfidModule);

const RfidReaderLayout: React.FC<IRfidReaderLayoutProps> = ({
  children,
  onCustomAction,
}) => {
  const inputRef = useRef<TextInput>(null);
  const navigation = useNavigation<StackNavigationProp<RootStackParamList>>();
  const { setStratumId, setSamplingPointId, setTreeId } = useInfo();
  const [loading, setLoading] = useState<boolean>(false);
  const [isRfidReaderConnected, setIsRfidReaderConnected] =
    useState<boolean>(false);
  const [rfidInput, setRfidInput] = useState<string>('');

  function clearStates() {
    setRfidInput('');
    setLoading(false);
  }

  function triggerSnackbar(text: string, backgroundColor: string) {
    clearStates();
    Snackbar.show({
      text,
      duration: Snackbar.LENGTH_SHORT,
      backgroundColor,
      action: {
        text: 'OK',
        textColor: colors.white,
        onPress: () => Snackbar.dismiss(),
      },
    });
  }

  async function handleOnPress() {
    if (onCustomAction) {
      onCustomAction();
      return;
    }
  }

  async function handleRfidInputChange(text: string) {
    setRfidInput(text);

    if (text.length === 10) {
      const rfidCode = text.trim();
      await handleOnReceiveRfidCode(rfidCode);
    }
  }

  async function handleOnReceiveRfidCode(rfidCode: string) {
    setLoading(true);
    try {
      const tree = realm.objects('Tree').filtered('rfid.code == $0', rfidCode);

      if (tree.length === 0) {
        triggerSnackbar(
          `Árvore com o RFID ${rfidCode} não foi encontrada.`,
          colors.statusRed,
        );
        return;
      }
      const treeId = tree[0].id as number;

      const samplingPoints = realm
        .objects('SamplingPoint')
        .filtered('ANY trees.id == $0', treeId) as any as ISamplingPoint[];
      const samplingPointId = samplingPoints[0].id as number;

      const operationalUnits = realm
        .objects('Strata')
        .filtered('ANY sampling_points.id == $0', samplingPointId);
      const operationalUnitId = operationalUnits[0].id as number;

      if (samplingPoints.length > 0) {
        setSamplingPointId({ samplingPointId });
        setStratumId({ stratumId: operationalUnitId });
        setTreeId({ treeId });
        setTimeout(() => {
          navigation.navigate('Collect', { title: 'Frutos sadios' });
          clearStates();
        }, 1000);
      } else {
        triggerSnackbar(
          `Árvore com o RFID ${rfidCode} não foi encontrada.`,
          colors.statusRed,
        );
      }
    } catch (error) {
      triggerSnackbar(
        `Erro ao buscar a árvore pelo RFID ${rfidCode}.`,
        colors.statusRed,
      );
    }
  }

  useEffect(() => {
    RfidModule.initialize(COMPATIBLE_VENDOR_IDS);

    const onTagReadListener = rfidEmitter.addListener('onTagRead', tag =>
      console.log(`rfid tag value: ${tag}`),
    );

    const onDeviceConnectedListener = rfidEmitter.addListener(
      'onDeviceConnected',
      message => {
        console.log(`rfid reader connected: ${message}`);
        setIsRfidReaderConnected(true);
      },
    );

    const onDeviceDisconnectedListener = rfidEmitter.addListener(
      'onDeviceDisconnected',
      message => {
        console.log(`rfid reader disconnected: ${message}`);
        setIsRfidReaderConnected(false);
      },
    );

    const onDeviceErrorListener = rfidEmitter.addListener(
      'onDeviceError',
      error => console.log(`error: ${error}`),
    );

    return () => {
      onTagReadListener.remove();
      onDeviceErrorListener.remove();
      onDeviceConnectedListener.remove();
      onDeviceDisconnectedListener.remove();
    };
  }, []);

  useEffect(() => {
    setRfidInput('');
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, []);

  return (
    <RfidReaderContainer>
      <TextInput
        ref={inputRef}
        value={rfidInput}
        onChangeText={handleRfidInputChange}
        blurOnSubmit={false}
        showSoftInputOnFocus={false}
        editable={true}
        pointerEvents="none"
        style={styles.hiddenInput}
      />

      <FloatingButton
        disabled={!isRfidReaderConnected}
        onPress={handleOnPress}
      />
      {children}

      <Modal transparent={true} animationType="fade" visible={loading}>
        <ModalContainer>
          <ModalContent>
            <ModalText>
              Buscando a árvore vinculada ao RFID: {rfidInput}
            </ModalText>

            <ActivityIndicator size="large" color={colors.primary} />
          </ModalContent>
        </ModalContainer>
      </Modal>
    </RfidReaderContainer>
  );
};

export default RfidReaderLayout;
