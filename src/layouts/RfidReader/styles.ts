import { StyleSheet } from 'react-native';
import styled from 'styled-components/native';
import { colors } from '@theme';

export const styles = StyleSheet.create({
  hiddenInput: { position: 'absolute', top: -9999, left: -9999 },
});

export const ModalContainer = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.5);
`;

export const ModalContent = styled.View`
  background-color: white;
  padding: 20px;
  border-radius: 10px;
  align-items: center;
  justify-content: center;

  display: flex;
  flex-direction: column;
  gap: 10px;
`;

export const ModalText = styled.Text`
  font-size: 18px;
  color: ${colors.black};
  font-weight: bold;
`;

export const RfidReaderContainer = styled.View`
  flex: 1;
`;

export const HiddenTextInput = styled.TextInput`
  position: absolute;
  top: -9999;
  left: -9999;
`;
