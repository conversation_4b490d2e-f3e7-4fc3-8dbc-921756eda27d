import React, { useEffect, useState } from 'react';
import { FlatList } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

import { ListItem } from '@components';
import { translate } from '@global/i18n';
import { realm } from '@database/database';
import { RfidReaderLayout } from '@layouts';
import { RootStackParamList } from '@routes.types';
import { useInfo } from '@store/slices/info/useInfo';
import { useAuth } from '@store/slices/auth/useAuth';
import { IProperty } from '@global/types/property.types';
import { IOperationalUnit } from '@global/types/stratum.types';
import { ISamplingPoint } from '@global/types/samplingPoint.types';
import { IHomogeneousArea } from '@global/types/homogeneousArea.types';

import { IProps } from './types';

const HomogeneousAreas: React.FC<IProps> = () => {
  const navigation = useNavigation<StackNavigationProp<RootStackParamList>>();
  const { propertyId, setHomogeneousAreaId } = useInfo();
  const { user } = useAuth();
  const [property, setProperty] = useState<IProperty>({} as IProperty);
  const [homogeneousAreas, setHomogeneousAreas] = useState<
    Array<IHomogeneousArea>
  >([]);

  function getProperty(): IProperty {
    const properties = realm.objects<Realm.Object & IProperty>('Property');
    return properties.filtered(`id=${propertyId}`)[0];
  }

  function getSamplingPoints(): Array<ISamplingPoint> {
    return realm.objects('SamplingPoint') as any;
  }

  function getStrata(): Array<IOperationalUnit> {
    return realm.objects('Strata') as any;
  }

  function _keyExtractor(item: IHomogeneousArea, index: number) {
    return `${item.id}-${index}`;
  }

  function _renderItem({ item }: { item: IHomogeneousArea }) {
    const handlePress = () => {
      setHomogeneousAreaId({ homogeneousAreaId: item.id });
      navigation.navigate('OperationalUnits', {
        subtitle: `${property.name} - ${translate('ha')}${item.id}`,
      });
    };

    return (
      <ListItem
        title={`${translate('homogeneousArea')} - ${translate('ha')}${item.id}`}
        onPress={handlePress}
      />
    );
  }

  useEffect(() => {
    const filteredProperty = getProperty();
    const unfilteredSamplingPoints = getSamplingPoints();
    const unfilteredStrata = getStrata();

    let samplingPointsIds: number[] = [];
    unfilteredSamplingPoints.map(sp => {
      if (sp.users_ids.length > 0) {
        sp.users_ids.map(u => {
          if (u.user_id === user?.id) {
            samplingPointsIds.push(sp.strata[0].id);
          }
        });
      }
    });

    samplingPointsIds = [...new Set(samplingPointsIds)];

    let homogeneousAreasIds = unfilteredStrata.map(s => {
      if (s.homogeneous_areas[0] && samplingPointsIds.indexOf(s.id) !== -1) {
        return s.homogeneous_areas[0].id;
      }
    });
    homogeneousAreasIds = [...new Set(homogeneousAreasIds)];

    const _homogeneousAreas = filteredProperty.homogeneous_areas.filter(
      homogeneousArea => {
        if (homogeneousAreasIds.indexOf(homogeneousArea.id) !== -1) {
          return homogeneousArea;
        }
      },
    );
    setProperty(filteredProperty);
    setHomogeneousAreas(_homogeneousAreas);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <RfidReaderLayout>
      <FlatList
        keyExtractor={_keyExtractor}
        renderItem={_renderItem}
        data={homogeneousAreas}
      />
    </RfidReaderLayout>
  );
};

export default HomogeneousAreas;
