import React from 'react';

import { translate } from '@global/i18n';
import { useAuth } from '@store/slices/auth/useAuth';
import farmerImg from '@assets/images/farmer.png';

import {
  Container,
  EmailText,
  NameText,
  ProfileContainer,
  UserDataContainer,
  UserDataLabel,
  UserDataRow,
  UserImage,
  UserImageContainer,
  UserPresentationContainer,
} from './styles';
import { masks, strings, time } from '@utils';

const Profile: React.FC = () => {
  const { user } = useAuth();

  return (
    <Container>
      <ProfileContainer>
        <UserPresentationContainer>
          <UserImageContainer>
            <UserImage source={farmerImg} />
          </UserImageContainer>
          <NameText>{strings.getAttrValue(user?.name)}</NameText>
          <EmailText>{strings.getAttrValue(user?.email)}</EmailText>
        </UserPresentationContainer>

        <UserDataContainer>
          <UserDataRow>
            <UserDataLabel>{translate('cpf')}</UserDataLabel>
            <UserDataLabel>
              {masks.cpfMask(strings.getAttrValue(user?.cpf))}
            </UserDataLabel>
          </UserDataRow>

          <UserDataRow>
            <UserDataLabel>{translate('birthDate')}</UserDataLabel>
            <UserDataLabel>
              {time.formatDate(strings.getAttrValue(user?.date_birth))}
            </UserDataLabel>
          </UserDataRow>

          <UserDataRow>
            <UserDataLabel>{translate('status')}</UserDataLabel>
            <UserDataLabel>
              {user?.status ? translate('active') : translate('inactive')}
            </UserDataLabel>
          </UserDataRow>
        </UserDataContainer>
      </ProfileContainer>
    </Container>
  );
};

export default Profile;
