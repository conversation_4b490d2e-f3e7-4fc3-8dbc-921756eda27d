import styled from 'styled-components/native';
import { colors, fonts, spacing } from '@theme';
import { Text } from '@components';

export const Container = styled.View`
  flex: 1;
  background-color: ${colors.primaryDarken};
  justify-content: flex-end;
`;

export const ProfileContainer = styled.View`
  flex: 1;
  max-height: 85%;
  border-top-left-radius: ${spacing.global.borderRadiusCircular}px;
  border-top-right-radius: ${spacing.global.borderRadiusCircular}px;
  background-color: ${colors.snow};
  padding: ${spacing.global.spacingMedium}px;
`;

export const UserPresentationContainer = styled.View`
  top: -15%;
  justify-content: center;
  align-items: center;
`;

export const UserImageContainer = styled.View`
  border-radius: ${spacing.global.borderRadiusCircular}px;
  padding-bottom: ${spacing.global.spacingSmall}px;
`;

export const UserImage = styled.Image`
  resize-mode: contain;
  max-height: 150px;
`;

export const NameText = styled(Text)`
  color: ${colors.dark};
  font-weight: ${fonts.fontWeight.bold};
  font-size: ${fonts.fontSize.large}px;
`;

export const EmailText = styled(Text)`
  font-size: ${fonts.fontSize.regular}px;
`;

export const UserDataContainer = styled.View`
  padding-horizontal: ${spacing.global.spacingMedium}px;
  gap: ${spacing.global.gap}px;
`;

export const UserDataRow = styled.View`
  flex-direction: row;
  justify-content: space-between;
  padding-vertical: ${spacing.global.spacingSmall}px;
`;

export const UserDataLabel = styled(Text)``;

export const UserDataLabelValue = styled(Text)``;
