import styled from 'styled-components/native';
import { colors, fonts, spacing } from '@theme';
import { Text } from '@components';

export const Container = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
  background-color: ${colors.primaryDarken};
  padding: ${spacing.global.spacingMedium}px;
  gap: ${spacing.global.gap}px;
`;

export const TitleContainer = styled.View`
  justify-content: center;
  align-items: center;
`;

export const LogoImage = styled.Image`
  resize-mode: contain;
  align-self: center;
  max-height: 200px;
`;

export const Title = styled(Text)`
  font-size: ${fonts.fontSize.xxlarge}px;
  color: ${colors.white};
  font-weight: ${fonts.fontWeight.bold};
`;

export const Subtitle = styled(Text)`
  font-size: ${fonts.fontSize.large}px;
  color: ${colors.white};
  font-weight: 500;
`;

export const LogosContainer = styled.View`
  flex-direction: row;
  align-items: center;
  justify-content: space-evenly;

  padding-vertical: ${spacing.global.spacingSmall}px;
  gap: ${spacing.global.gap}px;
  width: 110%;

  background-color: ${colors.white};
`;

export const Logo = styled.Image`
  resize-mode: contain;
  align-self: center;
  width: 70px;
  height: 70px;
`;

export const SentenceContainer = styled.View`
  flex: 1;
`;

export const AboutSentenceText = styled(Text)`
  color: ${colors.white};
  text-align: justify;
`;

export const VersionNumber = styled(Text)`
  text-align: center;
  color: ${colors.white};
  font-weight: 500;
`;
