import React from 'react';
import DeviceInfo from 'react-native-device-info';

import { translate } from '@global/i18n';
import logoImg from '@assets/images/logo.png';
import uescImg from '@assets/images/uesc-logo.png';
import proexImg from '@assets/images/proex-logo.png';
import ceplacImg from '@assets/images/ceplac-logo.png';

import {
  AboutSentenceText,
  Container,
  Logo,
  LogoImage,
  LogosContainer,
  SentenceContainer,
  Subtitle,
  Title,
  TitleContainer,
  VersionNumber,
} from './styles';

const logos = [uescImg, proexImg, logoImg, ceplacImg];

const About: React.FC = () => {
  return (
    <Container>
      <TitleContainer>
        <LogoImage source={logoImg} />
        <Title>ColetaCacau</Title>
        <VersionNumber>{DeviceInfo.getVersion()}</VersionNumber>
        <Subtitle>{translate('cultivationData')}</Subtitle>
      </TitleContainer>

      <LogosContainer>
        {logos.map((logo, index) => (
          <Logo key={index} source={logo} />
        ))}
      </LogosContainer>

      <SentenceContainer>
        <AboutSentenceText>{translate('sentenceAbout')}</AboutSentenceText>
      </SentenceContainer>
    </Container>
  );
};

export default About;
