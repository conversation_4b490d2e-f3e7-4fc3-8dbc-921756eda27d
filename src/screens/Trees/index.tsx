import React, { useEffect, useState } from 'react';
import { FlatList } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

import { colors } from '@theme';
import { realm } from '@database/database';
import { collect as collectUtils } from '@utils';
import { RootStackParamList } from '@routes.types';
// Store
import { useInfo } from '@store/slices/info/useInfo';
import { useCollect } from '@store/slices/collect/useCollect';
// Global
import { translate } from '@global/i18n';
import { ITree } from '@global/types/trees.types';
import { ISamplingPoint } from '@global/types/samplingPoint.types';
// Components
import { ListItem } from '@components';

import { IProps } from './types';
import { Container, FlatListHeader, FlatListHeaderLabel } from './styles';

const Trees: React.FC<IProps> = ({}) => {
  const { collect } = useCollect();
  const navigation = useNavigation<StackNavigationProp<RootStackParamList>>();
  const { samplingPointId, stratumId, setTreeId } = useInfo();
  const [trees, setTrees] = useState<Array<ITree>>([]);
  const [samplingPoint, setSamplingPoint] = useState<ISamplingPoint>();
  const [refreshing, setRefreshing] = useState<boolean>(false);

  function getSamplingPoint(): ISamplingPoint {
    const rawSamplingPoints = realm.objects<Realm.Object & ISamplingPoint>(
      'SamplingPoint',
    );
    return rawSamplingPoints.filtered(`id=${samplingPointId}`)[0];
  }

  function _keyExtractor(item: ITree) {
    return `${item.id}`;
  }

  function _renderHeader(): React.ReactElement {
    return (
      <FlatListHeader>
        <FlatListHeaderLabel>{`${translate('collection')} nº ${
          (samplingPoint?.ini_period as number) + 1
        }`}</FlatListHeaderLabel>
      </FlatListHeader>
    );
  }

  function _renderItem({ item }: { item: ITree }) {
    const label = item.alternative_label ? item.alternative_label : item.label;
    const treeWasVisited = collectUtils.checkTreeWasVisited(
      item.id,
      stratumId,
      samplingPointId,
      collect,
    );

    const handlePress = () => {
      setTreeId({ treeId: item.id });
      navigation.navigate('Collect', { title: 'Frutos sadios' });
    };

    return (
      <ListItem
        title={`${translate('tree')} ${label}`}
        onPress={handlePress}
        color={treeWasVisited ? colors.deepPetrolBlue : colors.white}
        backgroundColor={treeWasVisited ? colors.yellow : undefined}
        rightComponent={{
          label: treeWasVisited ? translate('completed') : translate('pending'),
          color: treeWasVisited ? colors.deepPetrolBlue : colors.yellow,
        }}
      />
    );
  }

  async function reloadData(): Promise<void> {
    setRefreshing(true);
    try {
    } catch (error) {
      console.error(error);
    } finally {
      setRefreshing(false);
    }
  }

  useEffect(() => {
    const filteredSamplingPoint = getSamplingPoint();
    const filteredTrees = filteredSamplingPoint.trees.map(
      t => t.status === 1 && t,
    ) as Array<ITree>;

    setTrees(filteredTrees);
    setSamplingPoint(filteredSamplingPoint);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <Container>
      <FlatList
        data={trees}
        refreshing={refreshing}
        keyExtractor={_keyExtractor}
        renderItem={_renderItem}
        ListHeaderComponent={_renderHeader}
        onRefresh={reloadData}
      />
    </Container>
  );
};

export default Trees;
