import styled from 'styled-components/native';
import { colors, fonts, spacing } from '@theme';
import { Text } from '@components';

export const Container = styled.View`
  flex: 1;
`;

export const FlatListHeader = styled.View`
  max-height: 120px;
  margin: 10px;
  padding-vertical: ${spacing.global.spacingMedium}px;
  background-color: ${colors.yellow};
  border-radius: 4px;
`;

export const FlatListHeaderLabel = styled(Text)`
  font-weight: ${fonts.fontWeight.bold};
  font-size: ${fonts.fontSize.regular}px;
  text-align: center;
`;
