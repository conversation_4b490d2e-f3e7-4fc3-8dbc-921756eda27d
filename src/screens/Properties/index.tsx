import React, { useEffect, useState } from 'react';
import { FlatList } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

import { RfidReaderLayout } from '@layouts';
import { EmptyMessage, ListItem } from '@components';
import { realm } from '@database/database';
import { HTTP_CODES } from '@global/constants';
import { RootStackParamList } from '@routes.types';
import { authService } from '@services/auth/auth.service';
import { IProperty } from '@global/types/property.types';
import { useInfo } from '@store/slices/info/useInfo';
import { useAuth } from '@store/slices/auth/useAuth';

import { IProps } from './types';
import { ISamplingPoint } from '@global/types/samplingPoint.types';
import { IOperationalUnit } from '@global/types/stratum.types';

const Properties: React.FC<IProps> = () => {
  const navigation = useNavigation<StackNavigationProp<RootStackParamList>>();
  const { auth, user, authenticate } = useAuth();
  const { setPropertyId } = useInfo();
  const [properties, setProperties] = useState<Array<IProperty>>([]);

  function getProperties(): Array<IProperty> {
    return realm.objects('Property') as any;
  }

  function getSamplingPoints(): Array<ISamplingPoint> {
    return realm.objects('SamplingPoint') as any;
  }

  function getOperationalUnits(): Array<IOperationalUnit> {
    return realm.objects('Strata') as any;
  }

  async function getLoggedUser(): Promise<void> {
    try {
      const { data, status } = await authService.me();
      if (status === HTTP_CODES.OK) {
        authenticate({ auth, user: data });
      }
    } catch (error) {
      console.error(error);
    }
  }

  function _keyExtractor(item: IProperty, index: number) {
    return `${item.id}-${index}`;
  }

  function _renderItem({ item }: { item: IProperty }) {
    const handlePress = () => {
      setPropertyId({ propertyId: item.id });
      navigation.navigate('HomogeneousAreas', { subtitle: item.name });
    };

    return <ListItem title={item.name} onPress={handlePress} />;
  }

  useEffect(() => {
    getLoggedUser();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  //! Cyclical problem in properties object
  useEffect(() => {
    const unfilteredProperties = getProperties();
    const samplingPoints = getSamplingPoints();
    const operationalUnits = getOperationalUnits();

    let _properties: Array<IProperty> = [];
    let operationalUnitsIds: number[] = [];
    let homogeneousAreasIds: number[] = [];

    samplingPoints.map(sp => {
      if (sp.users_ids.length > 0) {
        sp.users_ids.map(u => {
          if (u.user_id === user?.id) {
            if (!operationalUnitsIds.includes(sp.strata[0].id)) {
              operationalUnitsIds.push(sp.strata[0].id);
            }
          }
        });
      }
    });

    operationalUnitsIds.map(ouId => {
      operationalUnits.map(ou => {
        if (ou.id === ouId) {
          ou.homogeneous_areas.map(ha => homogeneousAreasIds.push(ha.id));
        }
      });
    });

    unfilteredProperties.map(p => {
      if (homogeneousAreasIds.includes(p.homogeneous_areas[0].id)) {
        _properties.push(p);
      }
    });
    setProperties(_properties);
  }, [user]);

  return (
    <RfidReaderLayout>
      <FlatList
        keyExtractor={_keyExtractor}
        renderItem={_renderItem}
        data={properties}
        ListEmptyComponent={
          <EmptyMessage
            title="Nenhum dado encontrato!"
            subtitle="Contate o administrador no email: <EMAIL>"
          />
        }
      />
    </RfidReaderLayout>
  );
};

export default Properties;
