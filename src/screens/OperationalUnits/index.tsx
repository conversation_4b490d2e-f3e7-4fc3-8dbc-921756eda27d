import React, { useEffect, useState } from 'react';
import axios from 'axios';
import { FlatList } from 'react-native';
import Snackbar from 'react-native-snackbar';
import NetInfo from '@react-native-community/netinfo';
import { StackNavigationProp } from '@react-navigation/stack';
import { CommonActions, useNavigation } from '@react-navigation/native';

import { colors, misc } from '@theme';
import { translate } from '@global/i18n';
import { realm } from '@database/database';
import { RfidReaderLayout } from '@layouts';
import { COLLECT_INTERVAL_DAYS, HTTP_CODES } from '@global/constants';
import { collect as collectUtils } from '@utils';
import { RootStackParamList } from '@routes.types';
import { useInfo } from '@store/slices/info/useInfo';
import { useAuth } from '@store/slices/auth/useAuth';
import { AlertValidation, ListItem } from '@components';
import { IProperty } from '@global/types/property.types';
import { useCollect } from '@store/slices/collect/useCollect';
import { IOperationalUnit } from '@global/types/stratum.types';
import { usePractice } from '@store/slices/practice/usePractice';
import { ISamplingPoint } from '@global/types/samplingPoint.types';
import { collectService } from '@services/collect/collect.service';
import { IHomogeneousArea } from '@global/types/homogeneousArea.types';
import { useDateControl } from '@store/slices/dateControl/useDateControl';
import { IAlertValidationStateProps } from '@global/types/alertValidation.types';

import { IProps } from './types';
import {
  FooterButton,
  FooterButtonContainer,
  FooterButtonLabel,
  FooterContainer,
} from './styles';

const OperationalUnits: React.FC<IProps> = () => {
  const { user } = useAuth();
  const navigation = useNavigation<StackNavigationProp<RootStackParamList>>();
  const { homogeneousAreaId, propertyId, setStratumId } = useInfo();
  const {
    practiceControl,
    setLastPracticeCollectDate,
    setLastFruitCollectDate,
  } = useDateControl();
  const { collect } = useCollect();
  const { initializeCurrentPractice } = usePractice();
  const [isUserResponsible, setIsUserResponsible] = useState<boolean>(false);
  const [property, setProperty] = useState<IProperty>();
  const [homogeneousArea, setHomogeneousArea] = useState<IHomogeneousArea>(
    {} as IHomogeneousArea,
  );
  const [operationalUnits, setOperationalUnits] =
    useState<Array<IOperationalUnit>>();

  const [loading, setLoading] = useState<boolean>(false);
  const [isConnected, setIsConnected] = useState<boolean | null>(false);
  const [showAlertValidation, setShowAlertValidation] =
    useState<boolean>(false);
  const [alertValidationProps, setAlertValidationProps] =
    useState<IAlertValidationStateProps>({
      title: 'Atenção!',
      subtitle: '',
      message: '',
      secondaryButtonLabel: '',
      onSecondaryButtonPress: () => undefined,
    });

  function getProperty(): IProperty {
    const properties = realm.objects<Realm.Object & IProperty>('Property');
    return properties.filtered(`id=${propertyId}`)[0];
  }

  function getHomogeneousArea(): IHomogeneousArea {
    const _homogeneousArea = realm.objects<Realm.Object & IHomogeneousArea>(
      'HomogeneousArea',
    );
    return _homogeneousArea.filtered(
      `id=${homogeneousAreaId} && strata.@count>0`,
    )[0];
  }

  function getSamplingPoints(): Array<ISamplingPoint> {
    return realm.objects('SamplingPoint') as any;
  }

  function practiceDataHasAlreadyBeenCollected() {
    const lastPractice = practiceControl
      .map(pc => {
        if (pc.homogeneous_area_id === homogeneousAreaId) {
          return pc;
        }
      })
      .filter(pc => pc !== undefined);

    const today = new Date();
    const timeDiff = Math.abs(
      new Date(lastPractice[0]?.previous as any).getTime() - today.getTime(),
    );
    const diffDays = Math.ceil(timeDiff / (1000 * 3600 * 24));
    return diffDays < COLLECT_INTERVAL_DAYS;
  }

  function handleNavigateToPracticeDataScreen(): void {
    const hasAlreadyBeenCollected = practiceDataHasAlreadyBeenCollected();
    const area1WasObserved = collectUtils.checkArea1WasObserved(
      propertyId,
      homogeneousAreaId,
      practiceControl,
    );
    const area2WasObserved = collectUtils.checkArea2WasObserved(
      propertyId,
      homogeneousAreaId,
      practiceControl,
    );

    if (hasAlreadyBeenCollected) {
      setAlertValidationProps({
        title: 'Atenção!',
        subtitle: '',
        message:
          'Os dados da prática dessa área já foram coletados. Aguarde até a próxima data de coleta.',
      });
      setShowAlertValidation(true);
      return;
    } else if (area1WasObserved) {
      navigation.navigate('PracticeAreaData');
    } else if (area2WasObserved) {
      setAlertValidationProps({
        title: 'Atenção!',
        subtitle: '',
        message: 'Os dados da prática dessa área já foram coletados.',
      });
      setShowAlertValidation(true);
      return;
    }

    initializeCurrentPractice({
      propertyId,
      homogeneousAreaId,
    });
    navigation.navigate('PracticeData');
  }

  function handleOnClose() {
    setShowAlertValidation(false);
    setAlertValidationProps({
      title: 'Atenção!',
      subtitle: '',
      message: '',
      secondaryButtonLabel: '',
      onSecondaryButtonPress: () => undefined,
    });
  }

  function _keyExtractor(item: IOperationalUnit, index: number) {
    return `${item.id}-${index}`;
  }

  function _renderItem({ item }: { item: IOperationalUnit }) {
    const handlePress = () => {
      setStratumId({ stratumId: item.id });
      navigation.navigate('SamplingPoints', {
        subtitle: `${property?.name} - ${homogeneousArea.label} - ${item.label}`,
        title: `${translate('samplingPoints')}`,
      });
    };

    return (
      <ListItem
        title={`${translate('operationalUnit')} ${item.id} - ${translate(
          'ou',
        )}${item.id}`}
        onPress={handlePress}
      />
    );
  }

  function updateLastFruitCollectDate() {
    const currentLength = collect.length;

    for (let i = 0; i < currentLength; i++) {
      const spLength = collect[i].sampling_points.length;
      for (let j = 0; j < spLength; j++) {
        if (collect[i].sampling_points[j].trees.length > 0) {
          setLastFruitCollectDate({
            samplingPointId: collect[i].sampling_points[j].sampling_point_id,
            date: new Date(),
          });
        }
      }
    }
  }

  async function handleSyncData() {
    setLoading(true);
    try {
      if (isConnected) {
        updateLastFruitCollectDate();
        await collectService.sendCollectData();

        Snackbar.show({
          text: 'Sucesso! Coleta enviada para o sistema.',
          duration: Snackbar.LENGTH_INDEFINITE,
          backgroundColor: colors.statusGreen,
          action: {
            text: 'OK',
            textColor: 'white',
            onPress: () => Snackbar.dismiss(),
          },
        });

        setTimeout(() => {
          navigation.dispatch(
            CommonActions.reset({
              index: 0,
              routes: [{ name: 'Home' }],
            }),
          );
        }, 2000);
      } else {
        setAlertValidationProps({
          title: 'Atenção!',
          subtitle: '',
          message:
            'Não foi possível enviar os dados para o sistema. Verifique sua conexão com a internet e tente novamente.',
          secondaryButtonLabel: 'OK',
          onSecondaryButtonPress: () => setShowAlertValidation(false),
        });
      }
    } catch (error) {
      console.error(error);
      if (
        axios.isAxiosError(error) &&
        error.response &&
        error.response.status === HTTP_CODES.UNAUTHORIZED
      ) {
        navigation.dispatch(
          CommonActions.reset({
            index: 0,
            routes: [{ name: 'Login' }],
          }),
        );
      } else {
        Snackbar.show({
          text: 'Erro! Não foi possível enviar os dados para o sistema. Tente mais tarde!',
          duration: Snackbar.LENGTH_INDEFINITE,
          backgroundColor: colors.statusRed,
          action: {
            text: 'OK',
            textColor: colors.white,
            onPress: () => Snackbar.dismiss(),
          },
        });
      }
    } finally {
      setLoading(false);
    }
  }

  function checkAnyTreeWasVisited() {
    const collectLength = collect.length;

    for (let i = 0; i < collectLength; i++) {
      const spLength = collect[i].sampling_points.length;
      for (let j = 0; j < spLength; j++) {
        if (collect[i].sampling_points[j].trees.length >= 1) {
          return true;
        }
      }
    }
    return false;
  }

  function _renderFooter() {
    const hasAnyTreeVisited = checkAnyTreeWasVisited();
    if (hasAnyTreeVisited) {
      const handleOnPress = () => {
        setShowAlertValidation(true);
        setAlertValidationProps({
          title: 'Atenção!',
          subtitle:
            'Tem certeza que deseja enviar os dados coletados das árvores até então?',
          message:
            'Clique em Concluir para confirmar o envio da coleta ou em Continuar para continuar a coleta de dados.',
          secondaryButtonLabel: 'Concluir',
          buttonLabel: 'Continuar',
          onSecondaryButtonPress: () => handleSyncData(),
        });
      };

      return (
        <FooterContainer>
          <FooterButtonContainer>
            <FooterButton
              disabled={loading}
              onPress={handleOnPress}
              style={misc.effects.boxShadow}>
              <FooterButtonLabel>
                {'Enviar coletas das árvores'}
              </FooterButtonLabel>
            </FooterButton>
          </FooterButtonContainer>
        </FooterContainer>
      );
    }
  }

  useEffect(() => {
    const filteredProperty = getProperty();
    const samplingPoints = getSamplingPoints();
    const filteredHomogeneousArea = getHomogeneousArea();

    let date = null;
    let previousDate = null;
    if (!homogeneousArea.visit) {
      date = new Date();
      date.setDate(date.getDate() - 18);
      previousDate = new Date(
        `${date.getFullYear}-${date.getMonth() + 1}-${date.getDate()}`,
      );
    } else {
      date = homogeneousArea.visit.date;
      previousDate = new Date(date.split(' ')[0]);
    }

    let lastPracticeRegister = [];
    if (!(practiceControl.length > 0)) {
      setLastPracticeCollectDate({
        homogeneousAreaId,
        date: previousDate,
      });
    } else {
      const findArea = practiceControl.findIndex(
        value => value.homogeneous_area_id === homogeneousAreaId,
      );

      if (findArea !== -1) {
        lastPracticeRegister = practiceControl
          .map(pc => {
            if (pc.homogeneous_area_id === homogeneousAreaId) {
              return pc;
            }
          })
          .filter(pc => pc !== undefined);

        if (
          lastPracticeRegister[0]?.previous &&
          previousDate.getTime() >
            new Date(lastPracticeRegister[0].previous).getTime()
        ) {
          setLastPracticeCollectDate({
            homogeneousAreaId,
            date: previousDate,
          });
        }
      } else {
        setLastPracticeCollectDate({ homogeneousAreaId, date: previousDate });
      }
    }

    let _isUserResponsible: boolean = false;
    if (filteredHomogeneousArea.user) {
      _isUserResponsible = user?.id === filteredHomogeneousArea.user.id;
    }

    const unfilteredOperationalUnits = filteredHomogeneousArea.strata;
    let operationalUnitsIds: number[] = [];
    samplingPoints
      .map(sp => {
        if (sp.users_ids.length > 0) {
          sp.users_ids.map(u => {
            if (u.user_id === user?.id) {
              operationalUnitsIds.push(sp.strata[0].id);
            }
          });
        }
      })
      .filter(id => id !== undefined);
    operationalUnitsIds = [...new Set(operationalUnitsIds)];

    const filteredOperationalUnits = unfilteredOperationalUnits
      .map(op => {
        if (operationalUnitsIds.indexOf(op.id) !== -1) {
          return op;
        }
      })
      .filter(data => data !== undefined) as Array<IOperationalUnit>;

    setProperty(filteredProperty);
    setHomogeneousArea(filteredHomogeneousArea);
    setOperationalUnits(filteredOperationalUnits);
    setIsUserResponsible(_isUserResponsible);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    NetInfo.fetch().then(state => {
      setIsConnected(state.isConnected);
    });
  }, []);

  return (
    <RfidReaderLayout>
      <AlertValidation
        visible={showAlertValidation}
        title={alertValidationProps.title}
        subtitle={alertValidationProps.subtitle}
        message={alertValidationProps.message}
        buttonLabel={alertValidationProps.buttonLabel}
        secondaryButtonLabel={alertValidationProps.secondaryButtonLabel}
        onSecondaryButtonPress={alertValidationProps.onSecondaryButtonPress}
        onClose={handleOnClose}
      />

      {isUserResponsible ? (
        <ListItem
          title={`${translate('collectPracticeData')} - ${translate('ha')} ${
            homogeneousArea.id
          }`}
          onPress={handleNavigateToPracticeDataScreen}
        />
      ) : (
        <ListItem
          title={
            'Você não é responsável pela coleta de dados da prática desta área homogênea.'
          }
          onPress={() => null}
        />
      )}
      <FlatList
        data={operationalUnits}
        keyExtractor={_keyExtractor}
        renderItem={_renderItem}
        ListFooterComponent={_renderFooter}
      />
    </RfidReaderLayout>
  );
};

export default OperationalUnits;
