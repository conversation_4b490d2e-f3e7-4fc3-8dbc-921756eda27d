import { Text } from '@components';
import { colors, spacing } from '@theme';
import styled from 'styled-components/native';

export const FooterContainer = styled.View`
  flex: 1;
  padding-vertical: ${spacing.global.spacingMedium}px;
  gap: ${spacing.global.gap}px;
`;

export const FooterButtonContainer = styled.View`
  padding-horizontal: ${spacing.global.spacingDefault}px;
`;

export const FooterButton = styled.Pressable`
  width: 100%;
  padding-vertical: ${spacing.global.spacingSmall}px;
  padding-horizontal: ${spacing.global.spacingMedium}px;
  background-color: ${colors.primary};
  border-radius: 4px;
`;

export const FooterButtonLabel = styled(Text)`
  color: ${colors.white};
  font-weight: 600;
  text-align: center;
`;
