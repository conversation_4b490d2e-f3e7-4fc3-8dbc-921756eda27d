import { StyleSheet, TextInput } from 'react-native';
import styled from 'styled-components/native';
import { Text } from '@components';
import { colors, spacing } from '@theme';
import { IRowStyleProps } from './types';

export const styles = StyleSheet.create({
  flatListContainerStyle: {
    backgroundColor: colors.primaryDarken,
    paddingBottom: 125,
  },
  selectContainerStyle: {
    marginHorizontal: 5,
    height: 30,
  },
});

export const Container = styled.View`
  flex: 1;
  background-color: ${colors.primaryDarken};
`;

export const Row = styled.View<IRowStyleProps>`
  background-color: ${({ isEven }) =>
    isEven ? colors.primary : colors.greenLighter};
  flex-direction: row;
  align-items: center;
  justify-content: space-between;

  padding-vertical: ${spacing.global.spacingMedium}px;
  padding-horizontal: ${spacing.global.spacingLarge}px;
`;

export const RowLabel = styled(Text)<{ color: string }>`
  color: ${({ color }) => color};
`;

export const FooterContainer = styled.View`
  flex: 1;
  padding-vertical: ${spacing.global.spacingMedium}px;
  gap: ${spacing.global.gap}px;
`;

export const SelectContainer = styled.View`
  width: 250px;
  margin-vertical: 10px;
  border-radius: 5px;
  gap: ${spacing.global.gap}px;
`;

export const FooterButtonContainer = styled.View`
  padding-horizontal: ${spacing.global.spacingLarge}px;
`;

export const FooterButton = styled.Pressable`
  width: 100%;
  padding-vertical: ${spacing.global.spacingSmall}px;
  padding-horizontal: ${spacing.global.spacingMedium}px;
  background-color: ${colors.primary};
  border-radius: 4px;
`;

export const FooterButtonLabel = styled(Text)`
  color: ${colors.white};
  font-weight: 600;
  text-align: center;
`;

export const TextareaInput = styled(TextInput).attrs(() => ({
  placeholderTextColor: colors.gray,
  selectionColor: colors.black,
}))`
  padding: ${spacing.global.spacingSmall}px;
  margin-horizontal: ${spacing.global.spacingMedium}px;
  margin-vertical: ${spacing.global.spacingSmall}px;
  background-color: ${colors.white};
  color: ${colors.black};
  border-radius: 5px;
  height: 100px;
  text-align-vertical: top;
`;
