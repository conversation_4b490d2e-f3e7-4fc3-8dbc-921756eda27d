import React, { useEffect, useMemo, useState } from 'react';
import axios from 'axios';
import { FlatList } from 'react-native';
import Snackbar from 'react-native-snackbar';
import NetInfo from '@react-native-community/netinfo';
import { CommonActions, useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

import { area, time } from '@utils';
import { colors, misc } from '@theme';
import { AlertValidation, CheckBox } from '@components';
import {
  IAreaInfo,
  IPracticeDataStage2,
} from '@global/types/visitInformation.types';
import { HTTP_CODES } from '@global/constants';
import { RootStackParamList } from '@routes.types';
import { useInfo } from '@store/slices/info/useInfo';
import { usePractice } from '@store/slices/practice/usePractice';
import { collectService } from '@services/collect/collect.service';
import { useDateControl } from '@store/slices/dateControl/useDateControl';

import { IPracticeAreaDataProps } from './types';
import {
  Container,
  FooterButton,
  FooterButtonContainer,
  FooterButtonLabel,
  FooterContainer,
  Row,
  RowLabel,
  SelectContainer,
  TextareaInput,
  styles,
} from './styles';

const PracticeAreaData: React.FC = ({}: IPracticeAreaDataProps) => {
  const { saveCurrentPracticeArea } = usePractice();
  const { homogeneousAreaId, propertyId } = useInfo();
  const { setLastPracticeCollectDate } = useDateControl();
  const navigation = useNavigation<StackNavigationProp<RootStackParamList>>();

  const [loading, setLoading] = useState<boolean>(false);
  const [isConnected, setIsConnected] = useState<boolean | null>(false);
  const [practiceAreaData, setPracticeAreaData] = useState<IPracticeDataStage2>(
    { flowering: '', refoliation: '', top: '', note: '', date: '' },
  );
  const [note, setNote] = useState<string>('');
  const [alertValidationVisibility, setAlertValidationVisibility] =
    useState<boolean>(false);

  const infoArea: Array<IAreaInfo> = useMemo(() => {
    const _infoArea = area.getAreaInfo((info: IAreaInfo) => info.id < 4);
    return _infoArea;
  }, []);

  function _keyExtractor(item: IAreaInfo) {
    return `${item.id}`;
  }

  function _renderItem({ item }: { item: IAreaInfo }) {
    const { id, label, key } = item;
    const isEven = id % 2;
    const defaultColor = isEven ? colors.white : colors.primary;
    const isLastItem = id === 3;

    return (
      <Row isEven={isEven}>
        <RowLabel color={defaultColor}>{label}</RowLabel>
        <SelectContainer>
          <CheckBox
            borderColor={defaultColor}
            labelColor={defaultColor}
            value={practiceAreaData[key as keyof IPracticeDataStage2] === 1}
            label={isLastItem ? 'Ruim (<30%)' : 'Nenhuma'}
            handleChange={() =>
              setPracticeAreaData(prevState => ({ ...prevState, [key]: 1 }))
            }
          />
          <CheckBox
            borderColor={defaultColor}
            labelColor={defaultColor}
            value={practiceAreaData[key as keyof IPracticeDataStage2] === 2}
            label={isLastItem ? 'Regular (>29%)(<60%)' : 'Pouca'}
            handleChange={() =>
              setPracticeAreaData(prevState => ({ ...prevState, [key]: 2 }))
            }
          />
          <CheckBox
            borderColor={defaultColor}
            labelColor={defaultColor}
            value={practiceAreaData[key as keyof IPracticeDataStage2] === 3}
            label={isLastItem ? 'Boa (>59%)' : 'Muita (>50%)'}
            handleChange={() =>
              setPracticeAreaData(prevState => ({ ...prevState, [key]: 3 }))
            }
          />
        </SelectContainer>
      </Row>
    );
  }

  function _renderFooter() {
    return (
      <FooterContainer>
        <TextareaInput
          onChangeText={setNote}
          multiline={true}
          numberOfLines={2}
          placeholder={'Outras informações:\n(máx: 255 caracters)'}
        />
        <FooterButtonContainer>
          <FooterButton
            disabled={loading}
            onPress={handleSubmit}
            style={misc.effects.boxShadow}>
            <FooterButtonLabel>
              {isConnected ? 'Enviar dados da prática' : 'Sem Internet'}
            </FooterButtonLabel>
          </FooterButton>
        </FooterButtonContainer>
      </FooterContainer>
    );
  }

  async function handleSyncData(): Promise<void> {
    if (isConnected) {
      try {
        setLoading(true);
        await collectService.sendPracticeCollectData();
        setLastPracticeCollectDate({
          homogeneousAreaId,
          date: new Date(),
        });

        const resetAction = CommonActions.reset({
          index: 0,
          routes: [{ name: 'Home' }],
        });

        Snackbar.show({
          text: 'Sucesso! Dados da prática enviados para o sistema.',
          duration: Snackbar.LENGTH_INDEFINITE,
          backgroundColor: colors.statusGreen,
          action: {
            text: 'OK',
            textColor: colors.white,
            onPress: () => Snackbar.dismiss(),
          },
        });

        setTimeout(() => {
          navigation.dispatch(resetAction);
        }, 2000);
      } catch (error) {
        console.error(error);
        if (
          axios.isAxiosError(error) &&
          error.response &&
          error.response.status === HTTP_CODES.UNAUTHORIZED
        ) {
        } else {
          Snackbar.show({
            text: 'Erro! Não foi possível enviar os dados da prática para o sistema. Tente mais tarde!',
            duration: Snackbar.LENGTH_INDEFINITE,
            backgroundColor: colors.statusRed,
            action: {
              text: 'OK',
              textColor: 'white',
              onPress: () => Snackbar.dismiss(),
            },
          });
        }
      } finally {
        setLoading(false);
      }
    }
  }

  async function handleSubmit(): Promise<void> {
    try {
      const datetime = time.formatCurrentDate(true);
      if (isConnected) {
        if (
          !(
            practiceAreaData.top === '' ||
            practiceAreaData.flowering === '' ||
            practiceAreaData.refoliation === ''
          )
        ) {
          saveCurrentPracticeArea({
            homogeneousAreaId,
            propertyId,
            practice: {
              ...practiceAreaData,
              note,
              date: datetime,
            },
          });
          await handleSyncData();
          setLastPracticeCollectDate({
            homogeneousAreaId,
            date: new Date(),
          });
        } else {
          Snackbar.show({
            text: 'Preencha todos os campos',
            duration: Snackbar.LENGTH_INDEFINITE,
            backgroundColor: colors.statusRed,
            action: {
              text: 'OK',
              textColor: colors.white,
              onPress: () => Snackbar.dismiss(),
            },
          });
        }
      } else {
        setAlertValidationVisibility(true);
      }
    } catch (error) {
      console.error(error);
    }
  }

  useEffect(() => {
    NetInfo.fetch().then(state => {
      setIsConnected(state.isConnected);
    });
  }, []);

  return (
    <Container>
      <AlertValidation
        visible={alertValidationVisibility}
        title="Atenção!"
        message="Você não está conectado à Internet.\nConecte seu dispositivo à rede ou volte mais tarde para completar a ação!"
        buttonLabel="Entendi"
        onClose={() => setAlertValidationVisibility(false)}
      />
      <FlatList
        data={infoArea}
        keyExtractor={_keyExtractor}
        renderItem={_renderItem}
        contentContainerStyle={styles.flatListContainerStyle}
        ListFooterComponent={_renderFooter}
      />
    </Container>
  );
};

export default PracticeAreaData;
