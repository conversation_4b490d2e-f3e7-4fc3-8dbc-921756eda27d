import React, { useEffect, useLayoutEffect, useMemo, useState } from 'react';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

import { misc } from '@theme';
import { collect as collectUtils, time, validator } from '@utils';
import { translate } from '@global/i18n';
import { realm } from '@database/database';
import { RootStackParamList } from '@routes.types';
import { TREE_VISIT_INITIAL_STATE } from '@global/constants';
import { ISamplingPoint } from '@global/types/samplingPoint.types';
import {
  IAdultStage1,
  IAdultStage2,
  IBilro,
  IMatureStage1,
  IMatureStage2,
  IMatureStage3,
  IMatureStage4,
  IMediumStage1,
  IMediumStage2,
  IMediumStage3,
  ISmall,
} from '@global/types/cocoa.types';
import { AlertValidation } from '@components';
import { ITree } from '@global/types/trees.types';
import { useInfo } from '@store/slices/info/useInfo';
import { IProperty } from '@global/types/property.types';
import { ITreeVisit } from '@global/types/treeVisit.types';
import { useCollect } from '@store/slices/collect/useCollect';
import { IHomogeneousArea } from '@global/types/homogeneousArea.types';
import { IAlertValidationStateProps } from '@global/types/alertValidation.types';

import {
  CollectContainer,
  FooterButton,
  FooterButtonLabel,
  FooterContainer,
  ScrollViewHeader,
  ScrollViewHeaderLabel,
} from './styles';
import { CocoaInputBox } from './components';
import { CocoaTypeMap } from './types';

const COCOA_TYPE_MAP: CocoaTypeMap = {
  0: 'mature',
  1: 'mature2',
  2: 'mature3',
  3: 'mature4',
  4: 'adult',
  5: 'adult2',
  6: 'medium',
  7: 'medium2',
  8: 'medium3',
  9: 'small',
  10: 'bobbin',
};
const CURRENT_YEAR_PLUS_ONE = new Date().getFullYear() + 1;

const Collect: React.FC = () => {
  const { samplingPointId, treeId } = useInfo();
  const { collect, saveCurrentCollect } = useCollect();
  const navigation = useNavigation<StackNavigationProp<RootStackParamList>>();
  const [isReviewing, setIsReviewing] = useState<boolean>(false);
  const [showAlertValidation, setShowAlertValidation] =
    useState<boolean>(false);
  const [alertValidationProps, setAlertValidationProps] =
    useState<IAlertValidationStateProps>({
      title: 'Atenção!',
      subtitle: '',
      message: '',
      secondaryButtonLabel: '',
      onSecondaryButtonPress: () => undefined,
    });
  const [samplingPoint, setSamplingPoint] = useState<ISamplingPoint>();
  const [homogeneousArea, setHomogeneousArea] = useState<IHomogeneousArea>();
  const [property, setProperty] = useState<IProperty>();
  const [treeVisit, setTreeVisit] = useState<ITreeVisit>(
    TREE_VISIT_INITIAL_STATE as ITreeVisit,
  );
  const [treeReviewData, setTreeReviewData] = useState<ITreeVisit>(
    TREE_VISIT_INITIAL_STATE as ITreeVisit,
  );
  const [lastVisitData, setLastVisitData] = useState<ITreeVisit>(
    TREE_VISIT_INITIAL_STATE as ITreeVisit,
  );
  const [treeVisitMemo, setTreeVisitMemo] = useState<ITreeVisit>(
    TREE_VISIT_INITIAL_STATE as ITreeVisit,
  );
  const [bobbin, setBobbin] = useState<IBilro>({
    total: 0,
  });
  const [small, setSmall] = useState<ISmall>({
    piece: 0,
    loss: 0,
    total: 0,
    witchs_broom: 0,
  });
  const [mediumStage1, setMediumStage1] = useState<IMediumStage1>({
    piece: 0,
    loss: 0,
    total: 0,
  });
  const [mediumStage2, setMediumStage2] = useState<IMediumStage2>({
    piece: 0,
    loss: 0,
    witchs_broom: 0,
    rat: 0,
    rotten: 0,
    total: 0,
  });
  const [mediumStage3, setMediumStage3] = useState<IMediumStage3>({
    piece: 0,
    loss: 0,
    witchs_broom: 0,
    rat: 0,
    rotten: 0,
    harvested: 0,
    total: 0,
  });
  const [adultStage1, setAdultStage1] = useState<IAdultStage1>({
    loss: 0,
    witchs_broom: 0,
    rat: 0,
    rotten: 0,
    harvested: 0,
    total: 0,
  });
  const [adultStage2, setAdultStage2] = useState<IAdultStage2>({
    loss: 0,
    witchs_broom: 0,
    rat: 0,
    rotten: 0,
    harvested: 0,
    total: 0,
  });
  const [matureStage1, setMatureStage1] = useState<IMatureStage1>({
    loss: 0,
    witchs_broom: 0,
    rat: 0,
    rotten: 0,
    harvested: 0,
    total: 0,
  });
  const [matureStage2, setMatureStage2] = useState<IMatureStage2>({
    loss: 0,
    witchs_broom: 0,
    rat: 0,
    rotten: 0,
    harvested: 0,
    total: 0,
  });
  const [matureStage3, setMatureStage3] = useState<IMatureStage3>({
    loss: 0,
    witchs_broom: 0,
    rat: 0,
    rotten: 0,
    harvested: 0,
    total: 0,
  });
  const [matureStage4, setMatureStage4] = useState<IMatureStage4>({
    loss: 0,
    witchs_broom: 0,
    rat: 0,
    rotten: 0,
    harvested: 0,
    total: 0,
  });
  const [treeInfo, setTreeInfo] = useState<ITree>();

  const currentDate = useMemo(() => time.formatCurrentDate(), []);
  const totalHealthyFruits = useMemo(
    () => collectUtils.calculateTotalHealthyFruits(treeVisit),
    [treeVisit],
  );
  const totalUnhealthyFruits = useMemo(
    () => collectUtils.calculateTotalUnhealthyFruits(treeVisit),
    [treeVisit],
  );

  const headerLabel = useMemo(() => {
    return treeInfo && samplingPoint
      ? `${property?.name} - ${homogeneousArea?.label} - ${
          samplingPoint?.strata[0].label
        } - PA${samplingPoint?.label}\nColeta ${
          samplingPoint?.ini_period + 1
        } - ${translate('tree')} ${treeInfo.label}\nSafra ${
          samplingPoint?.harvest || CURRENT_YEAR_PLUS_ONE
        }/${samplingPoint?.harvest + 1} - Ano ${
          samplingPoint.year
        }\n${currentDate}`
      : null;
  }, [treeInfo, samplingPoint, homogeneousArea, property, currentDate]);

  function getHomogeneousArea(strataId: number): IHomogeneousArea {
    const rawHomogeneousArea = realm.objects<Realm.Object & IHomogeneousArea>(
      'HomogeneousArea',
    );
    return rawHomogeneousArea.filtered(`strata.id=${strataId}`)[0];
  }

  function getProperty(homogeneousAreaId: number): IProperty {
    const rawProperty = realm.objects<Realm.Object & IProperty>('Property');
    return rawProperty.filtered(`homogeneous_areas.id=${homogeneousAreaId}`)[0];
  }

  function getSamplingPoint(): ISamplingPoint {
    const rawSamplingPoints = realm.objects<Realm.Object & ISamplingPoint>(
      'SamplingPoint',
    );
    return rawSamplingPoints.filtered(`id=${samplingPointId}`)[0];
  }

  function getTreeVisit(): Array<ITreeVisit> {
    const rawTreeVisit = realm.objects<Realm.Object & Array<ITreeVisit>>(
      'TreeVisit',
    );
    return rawTreeVisit.filtered(`tree.id=${treeId}`) as any;
  }

  function getBobbin(treeVisitId: number): IBilro {
    const rawBobbin = realm.objects<Realm.Object & IBilro>('Bobbin');
    return rawBobbin.filtered(`treeVisit.id=${treeVisitId}`)[0];
  }

  function getSmall(treeVisitId: number): ISmall {
    const rawSmall = realm.objects<Realm.Object & ISmall>('Small');
    return rawSmall.filtered(`treeVisit.id=${treeVisitId}`)[0];
  }

  function getMediumStage1(treeVisitId: number): IMediumStage1 {
    const rawMediumStage1 = realm.objects<Realm.Object & IMediumStage1>(
      'MediumStage1',
    );
    return rawMediumStage1.filtered(`treeVisit.id=${treeVisitId}`)[0];
  }

  function getMediumStage2(treeVisitId: number): IMediumStage2 {
    const rawMediumStage2 = realm.objects<Realm.Object & IMediumStage2>(
      'MediumStage2',
    );
    return rawMediumStage2.filtered(`treeVisit.id=${treeVisitId}`)[0];
  }

  function getMediumStage3(treeVisitId: number): IMediumStage3 {
    const rawMediumStage3 = realm.objects<Realm.Object & IMediumStage3>(
      'MediumStage3',
    );
    return rawMediumStage3.filtered(`treeVisit.id=${treeVisitId}`)[0];
  }

  function getAdultStage1(treeVisitId: number): IAdultStage1 {
    const rawAdultStage1 = realm.objects<Realm.Object & IAdultStage1>(
      'AdultStage1',
    );
    return rawAdultStage1.filtered(`treeVisit.id=${treeVisitId}`)[0];
  }

  function getAdultStage2(treeVisitId: number): IAdultStage2 {
    const rawAdultStage2 = realm.objects<Realm.Object & IAdultStage2>(
      'AdultStage2',
    );
    return rawAdultStage2.filtered(`treeVisit.id=${treeVisitId}`)[0];
  }

  function getMatureStage1(treeVisitId: number): IMatureStage1 {
    const rawMatureStage1 = realm.objects<Realm.Object & IMatureStage1>(
      'MatureStage1',
    );
    return rawMatureStage1.filtered(`treeVisit.id=${treeVisitId}`)[0];
  }

  function getMatureStage2(treeVisitId: number): IMatureStage2 {
    const rawMatureStage2 = realm.objects<Realm.Object & IMatureStage2>(
      'MatureStage2',
    );
    return rawMatureStage2.filtered(`treeVisit.id=${treeVisitId}`)[0];
  }

  function getMatureStage3(treeVisitId: number): IMatureStage3 {
    const rawMatureStage3 = realm.objects<Realm.Object & IMatureStage3>(
      'MatureStage3',
    );
    return rawMatureStage3.filtered(`treeVisit.id=${treeVisitId}`)[0];
  }

  function getMatureStage4(treeVisitId: number): IMatureStage4 {
    const rawMatureStage4 = realm.objects<Realm.Object & IMatureStage4>(
      'MatureStage4',
    );
    return rawMatureStage4.filtered(`treeVisit.id=${treeVisitId}`)[0];
  }

  function handleOnClose() {
    setShowAlertValidation(false);
    setAlertValidationProps({
      title: 'Atenção!',
      subtitle: '',
      message: '',
      secondaryButtonLabel: '',
      onSecondaryButtonPress: () => undefined,
    });
  }

  function handleSaveValidation(data: any) {
    saveCurrentCollect({
      stratumId: samplingPoint?.strata[0]?.id as number,
      samplingPointId: samplingPointId,
      collect: data,
    });
    navigation.goBack();
  }

  function handleValueInputChange(
    id: number,
    cocoaStageAttribute: string,
    value: string,
  ) {
    const cocoaType = COCOA_TYPE_MAP[id as keyof typeof COCOA_TYPE_MAP];
    const newValue = value || '0';
    if (isReviewing) {
      setLastVisitData(prevState => ({
        ...prevState,
        [cocoaType]: {
          ...prevState[cocoaType],
          [cocoaStageAttribute]: parseInt(newValue, 10),
        },
      }));
    } else {
      setTreeVisitMemo(prevState => ({
        ...prevState,
        [cocoaType]: {
          ...prevState[cocoaType],
          [cocoaStageAttribute]: parseInt(newValue, 10),
        },
      }));
    }

    setTreeVisit(prevState => ({
      ...prevState,
      [cocoaType]: {
        ...prevState[cocoaType],
        [cocoaStageAttribute]: parseInt(newValue, 10),
      },
    }));

    isReviewing
      ? handleComputeTotalFruitReview(id, cocoaStageAttribute)
      : handleComputeTotalFruit(id, cocoaStageAttribute);
  }

  function handleComputeTotalFruit(id: number, cocoaStageAttribute: string) {
    const cocoaType = COCOA_TYPE_MAP[id as keyof typeof COCOA_TYPE_MAP];
    const previousStageValue = treeVisitMemo[cocoaType][cocoaStageAttribute];

    if (cocoaType !== 'bobbin') {
      setTreeVisit((prevState: any) => ({
        ...prevState,
        [cocoaType]: {
          ...prevState[cocoaType],
          total:
            prevState[cocoaType][cocoaStageAttribute] > previousStageValue
              ? prevState[cocoaType]?.total +
                previousStageValue -
                prevState[cocoaType][cocoaStageAttribute]
              : prevState[cocoaType]?.total +
                (previousStageValue -
                  prevState[cocoaType][cocoaStageAttribute]),
        },
      }));

      setTreeVisitMemo((prevState: any) => ({
        ...prevState,
        [cocoaType]: {
          ...treeVisit[cocoaType],
          [cocoaStageAttribute]: treeVisit[cocoaType][cocoaStageAttribute],
        },
      }));
    }
  }

  function handleComputeTotalFruitReview(
    id: number,
    cocoaStageAttribute: string,
  ) {
    const cocoaType = COCOA_TYPE_MAP[id as keyof typeof COCOA_TYPE_MAP];
    const previousStageValue = lastVisitData[cocoaType][cocoaStageAttribute];

    if (cocoaType !== 'bobbin') {
      setTreeVisit((prevState: any) => ({
        ...prevState,
        [cocoaType]: {
          ...prevState[cocoaType],
          total:
            prevState[cocoaType][cocoaStageAttribute] > previousStageValue
              ? prevState[cocoaType]?.total +
                previousStageValue -
                prevState[cocoaType][cocoaStageAttribute]
              : prevState[cocoaType]?.total +
                (previousStageValue -
                  prevState[cocoaType][cocoaStageAttribute]),
        },
      }));

      setLastVisitData((prevState: any) => ({
        ...prevState,
        [cocoaType]: {
          ...treeVisit[cocoaType],
          [cocoaStageAttribute]: treeVisit[cocoaType][cocoaStageAttribute],
        },
      }));
    }
  }

  function handlePreviousValueChange(value: string) {
    const newValue = value === '-' ? '0' : value || '0';

    setTreeVisit(prevState => ({
      ...prevState,
      bobbinPV: {
        ...prevState.bobbinPV,
        total: parseInt(newValue, 10),
      },
      small: {
        ...prevState.small,
        total: parseInt(newValue, 10),
        piece: 0,
        loss: 0,
        witchs_broom: 0,
      },
    }));

    setTreeVisitMemo({
      ...treeVisit,
      small: {
        ...treeVisit.small,
        total: 0,
        piece: 0,
        loss: 0,
        witchs_broom: 0,
      },
    });
  }

  function handleConfirm(): void {
    const totalSmall =
      treeVisit.small.piece +
      treeVisit.small.loss +
      treeVisit.small.witchs_broom;
    if (totalSmall > treeVisit.bobbinPV.total) {
      handlePreviousValueChange(totalSmall.toString());
    }
  }

  function validate() {
    const result = validator.validateCollect(treeVisit, {
      bobbin,
      bobbinPV: treeVisit.bobbinPV,
      small,
      medium: mediumStage1,
      medium2: mediumStage2,
      medium3: mediumStage3,
      adult: adultStage1,
      adult2: adultStage2,
      mature: matureStage1,
      mature2: matureStage2,
      mature3: matureStage3,
      mature4: matureStage4,
    });

    if (result.isError) {
      setShowAlertValidation(true);
      setAlertValidationProps({
        title: 'Atenção!',
        subtitle: result.title,
        message: result.message,
      });
    } else {
      setShowAlertValidation(true);
      setAlertValidationProps({
        title: 'Sucesso!',
        subtitle: 'Dados da coleta validados.',
        message:
          'Clique em Concluir para finalizar a coleta desta árvore ou em Revisar para revisar a sua coleta. Lembre-se que uma vez concluída não poderá mais ser alterada.',
        buttonLabel: 'Revisar',
        secondaryButtonLabel: 'Concluir',
        onSecondaryButtonPress: () => {
          handleSaveValidation({
            tree_id: treeInfo?.id,
            data: {
              bobbin: treeVisit.bobbin,
              bobbinPV: treeVisit.bobbinPV,
              small: treeVisit.small,
              medium: treeVisit.medium,
              medium2: treeVisit.medium2,
              medium3: treeVisit.medium3,
              adult: treeVisit.adult,
              adult2: treeVisit.adult2,
              mature: treeVisit.mature,
              mature2: treeVisit.mature2,
              mature3: treeVisit.mature3,
              mature4: treeVisit.mature,
            },
          });
        },
      });
    }
  }

  useEffect(() => {
    let samplingPointData: any[] = [];
    collect.map(c => {
      c.sampling_points.find(sp => {
        if (sp.sampling_point_id === samplingPointId) {
          samplingPointData.push(sp);
        }
      });
    });

    if (samplingPointData && samplingPointData.length > 0) {
      samplingPointData[0]?.trees.map((t: any) => {
        if (t.tree_id === treeId) {
          setIsReviewing(true);
          const _treeReviewData = samplingPointData[0].trees.find(
            (spTree: any) => spTree.tree_id === treeId,
          );
          setTreeReviewData(_treeReviewData.data);
        }
      });
    }

    const filteredSamplingPoint = getSamplingPoint();
    const filteredHomogeneousArea = getHomogeneousArea(
      filteredSamplingPoint.strata[0].id,
    );
    const filteredProperty = getProperty(filteredHomogeneousArea.id);
    const filteredTreeVisit = getTreeVisit();

    const lengthTreeVisit = filteredTreeVisit.length - 2;
    const _treeInfo = filteredTreeVisit[lengthTreeVisit].tree[0];
    const treeVisitId = filteredTreeVisit[lengthTreeVisit].id;

    const _bobbin = getBobbin(treeVisitId);
    const _small = getSmall(treeVisitId);
    const _mediumStage1 = getMediumStage1(treeVisitId);
    const _mediumStage2 = getMediumStage2(treeVisitId);
    const _mediumStage3 = getMediumStage3(treeVisitId);
    const _adultStage1 = getAdultStage1(treeVisitId);
    const _adultStage2 = getAdultStage2(treeVisitId);
    const _matureStage1 = getMatureStage1(treeVisitId);
    const _matureStage2 = getMatureStage2(treeVisitId);
    const _matureStage3 = getMatureStage3(treeVisitId);
    const _matureStage4 = getMatureStage4(treeVisitId);

    setBobbin(_bobbin);
    setSmall(_small);
    setMediumStage1(_mediumStage1);
    setMediumStage2(_mediumStage2);
    setMediumStage3(_mediumStage3);
    setAdultStage1(_adultStage1);
    setAdultStage2(_adultStage2);
    setMatureStage1(_matureStage1);
    setMatureStage2(_matureStage2);
    setMatureStage3(_matureStage3);
    setMatureStage4(_matureStage4);
    setTreeInfo(_treeInfo);
    setSamplingPoint(filteredSamplingPoint);
    setHomogeneousArea(filteredHomogeneousArea);
    setProperty(filteredProperty);

    setTreeVisit(prevState => ({
      ...prevState,
      id: treeId,
      bobbinPV: {
        ...prevState.bobbinPV,
        total: parseInt(_bobbin?.total.toString() || '0', 10),
      },
    }));

    const shiftedStages = [
      ['small', _bobbin],
      ['medium', _small],
      ['medium2', _mediumStage1],
      ['medium3', _mediumStage2],
      ['adult', _mediumStage3],
      ['adult2', _adultStage1],
      ['mature', _adultStage2],
      ['mature2', _matureStage1],
      ['mature3', _matureStage2],
      ['mature4', _matureStage3],
    ];

    if (isReviewing) {
      for (const [currentStage, previousStage] of shiftedStages) {
        setTreeVisit((prevState: any) => ({
          ...prevState,
          ...treeReviewData,
          [currentStage as any]: {
            ...prevState[currentStage],
            ...treeReviewData[currentStage],
            total:
              currentStage === 'mature4'
                ? parseInt(previousStage?.total.toString() ?? 0, 10) +
                  parseInt(_matureStage4?.total.toString(), 10)
                : parseInt(previousStage?.total ?? 0, 10),
          },
        }));

        setLastVisitData((prevState: any) => ({
          ...prevState,
          [currentStage as any]: {
            ...treeReviewData[currentStage],
          },
        }));
      }
    } else {
      for (const [currentStage, previousStage] of shiftedStages) {
        setTreeVisit((prevState: any) => ({
          ...prevState,
          [currentStage as any]: {
            ...prevState[currentStage],
            total:
              currentStage === 'mature4'
                ? parseInt(previousStage?.total.toString() ?? 0, 10) +
                  parseInt(_matureStage4?.total.toString(), 10)
                : parseInt(previousStage?.total ?? 0, 10),
          },
        }));
      }
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isReviewing]);

  useLayoutEffect(() => {
    navigation.setParams({
      subtitle: `Frutos Sadios: ${totalHealthyFruits}`,
      title: `Frutos Nao Sadios: ${totalUnhealthyFruits}`,
    });
  }, [navigation, totalHealthyFruits, totalUnhealthyFruits]);

  return (
    <CollectContainer showsVerticalScrollIndicator={false}>
      <ScrollViewHeader>
        <ScrollViewHeaderLabel>{headerLabel}</ScrollViewHeaderLabel>
      </ScrollViewHeader>

      <CocoaInputBox
        title="0-21"
        backgroundColor="#A5EBA4"
        data={treeVisit.bobbin}
        onChangeTextInput={handleValueInputChange}
      />

      <CocoaInputBox
        title="21-42"
        backgroundColor="#82C782"
        data={treeVisit.small}
        bobbinPreviousVisit={treeVisit.bobbinPV}
        isReviewing={isReviewing}
        onChangeTextInput={handleValueInputChange}
        onChangeSmallInput={handlePreviousValueChange}
        confirm={handleConfirm}
        compute={handleComputeTotalFruit}
      />

      <CocoaInputBox
        title="42-63"
        backgroundColor="#5FA361"
        data={treeVisit.medium}
        isReviewing={isReviewing}
        onChangeTextInput={handleValueInputChange}
        compute={handleComputeTotalFruit}
      />

      <CocoaInputBox
        title="63-84"
        backgroundColor="#3D8142"
        data={treeVisit.medium2}
        isReviewing={isReviewing}
        onChangeTextInput={handleValueInputChange}
        compute={handleComputeTotalFruit}
      />

      <CocoaInputBox
        title="84-105"
        backgroundColor="#3DA254"
        data={treeVisit.medium3}
        isReviewing={isReviewing}
        onChangeTextInput={handleValueInputChange}
        compute={handleComputeTotalFruit}
      />

      <CocoaInputBox
        title="105-126"
        backgroundColor="#88B657"
        data={treeVisit.adult}
        isReviewing={isReviewing}
        onChangeTextInput={handleValueInputChange}
        compute={handleComputeTotalFruit}
      />

      <CocoaInputBox
        title="126-147"
        backgroundColor="#C6C967"
        data={treeVisit.adult2}
        isReviewing={isReviewing}
        onChangeTextInput={handleValueInputChange}
        compute={handleComputeTotalFruit}
      />

      <CocoaInputBox
        title="147-168"
        backgroundColor="#F1C96E"
        data={treeVisit.mature}
        isReviewing={isReviewing}
        onChangeTextInput={handleValueInputChange}
        compute={handleComputeTotalFruit}
      />

      <CocoaInputBox
        title="168-189"
        backgroundColor="#F69048"
        data={treeVisit.mature2}
        isReviewing={isReviewing}
        onChangeTextInput={handleValueInputChange}
        compute={handleComputeTotalFruit}
      />

      <CocoaInputBox
        title="189-210"
        backgroundColor="#CB6C25"
        data={treeVisit.mature3}
        isReviewing={isReviewing}
        onChangeTextInput={handleValueInputChange}
        compute={handleComputeTotalFruit}
      />

      <CocoaInputBox
        title="> 210"
        backgroundColor="#A14900"
        data={treeVisit.mature4}
        isReviewing={isReviewing}
        onChangeTextInput={handleValueInputChange}
        compute={handleComputeTotalFruit}
      />

      <FooterContainer>
        <FooterButton onPress={validate} style={misc.effects.boxShadow}>
          <FooterButtonLabel>Validar</FooterButtonLabel>
        </FooterButton>
      </FooterContainer>

      <AlertValidation
        visible={showAlertValidation}
        title={alertValidationProps.title}
        subtitle={alertValidationProps.subtitle}
        message={alertValidationProps.message}
        buttonLabel={alertValidationProps.buttonLabel}
        secondaryButtonLabel={alertValidationProps.secondaryButtonLabel}
        onSecondaryButtonPress={alertValidationProps.onSecondaryButtonPress}
        onClose={handleOnClose}
      />
    </CollectContainer>
  );
};

export default Collect;
