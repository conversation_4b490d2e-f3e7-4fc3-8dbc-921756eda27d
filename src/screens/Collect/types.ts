import { IBilro, ISmall } from '@global/types/cocoa.types';
import { IVisitInformation } from '@global/types/visitInformation.types';

interface ICollectRealm {
  visitInformation: IVisitInformation;
  small: ISmall;
  bobbin: IBilro;
}

export type CocoaTypeMap = {
  0: 'mature';
  1: 'mature2';
  2: 'mature3';
  3: 'mature4';
  4: 'adult';
  5: 'adult2';
  6: 'medium';
  7: 'medium2';
  8: 'medium3';
  9: 'small';
  10: 'bobbin';
};

export type { ICollectRealm };
