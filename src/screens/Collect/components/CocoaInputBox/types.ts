import { IBilro } from '@global/types/cocoa.types';

interface ICocoaInputBoxProps {
  title: string;
  bobbinPreviousVisit?: IBilro;
  backgroundColor: string;
  isReviewing?: boolean;
  editable?: boolean;
  data: any;
  confirm?: (id: number, value: string) => void;
  compute?: (id: number, value: string) => void;
  onChangeTextInput: (id: number, key: string, value: string) => void;
  onChangeSmallInput?: (value: string) => void;
  onChangeValueInputReview?: (id: number, value: string) => void;
}

export type { ICocoaInputBoxProps };
