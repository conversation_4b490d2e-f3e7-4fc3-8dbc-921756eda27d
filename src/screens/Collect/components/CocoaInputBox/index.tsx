import React, { useMemo } from 'react';
import { NumberInput } from '@components';
import { misc } from '@theme';

import {
  Container,
  InputGroupContainer,
  HealthyFruitsNumberInputContainer,
  HealthyFruitsNumberInputLabel,
  Separator,
  Title,
  TitleContainer,
  NumberInputContainer,
  NumberInputLabel,
} from './styles';
import { ICocoaInputBoxProps } from './types';

const TITLE_MAP = {
  '147-168': 0,
  '168-189': 1,
  '189-210': 2,
  '> 210': 3,
  '105-126': 4,
  '126-147': 5,
  '42-63': 6,
  '63-84': 7,
  '84-105': 8,
  '21-42': 9,
  '0-21': 10,
};

const CocoaInputBox: React.FC<ICocoaInputBoxProps> = ({
  title,
  data,
  backgroundColor,
  editable,
  isReviewing,
  bobbinPreviousVisit,
  onChangeTextInput,
  onChangeSmallInput,
  confirm,
  compute,
}) => {
  const id: number = useMemo(
    () => TITLE_MAP[title as keyof typeof TITLE_MAP] ?? 0,
    [title],
  );

  function handleCompute(value: string) {
    if (compute) {
      compute(id, value);
    }
  }

  function handleConfirm(value: string): void {
    if (confirm) {
      confirm(id, value);
    }
  }

  return (
    <Container style={[{ backgroundColor }, misc.effects.boxShadow]}>
      <TitleContainer>
        <Title>{`${title} dias`}</Title>
        <HealthyFruitsNumberInputContainer>
          <HealthyFruitsNumberInputLabel>
            Frutos sadios
          </HealthyFruitsNumberInputLabel>

          {title === '0-21' ? (
            <NumberInput
              value={data?.total}
              variant="secondary"
              onChangeText={value => onChangeTextInput(id, 'total', value)}
            />
          ) : title !== '21-42' ? (
            <NumberInput
              editable={false}
              value={data?.total}
              variant="primary"
            />
          ) : (
            <NumberInput
              editable={editable}
              value={data?.total}
              variant="secondary"
              onBlur={() => confirm && confirm(id, 'total')}
              onChangeText={value =>
                onChangeSmallInput
                  ? value === '0' || ''
                    ? onChangeSmallInput(`${bobbinPreviousVisit?.total}`)
                    : onChangeSmallInput(value)
                  : null
              }
            />
          )}
        </HealthyFruitsNumberInputContainer>
      </TitleContainer>

      {title !== '0-21' && <Separator />}

      <InputGroupContainer>
        {/* mature | adult | medium3 */}
        {(id === 0 ||
          id === 1 ||
          id === 2 ||
          id === 3 ||
          id === 4 ||
          id === 5 ||
          id === 8) && (
          <NumberInputContainer>
            {data?.total !== 0 && !isReviewing ? (
              <NumberInput
                value={data?.harvested}
                onChangeText={value =>
                  onChangeTextInput(id, 'harvested', value)
                }
              />
            ) : (data?.harvested !== 0 || data?.total !== 0) && isReviewing ? (
              <>
                {data?.harvested !== 0 ? (
                  <NumberInput
                    editable={isReviewing}
                    value={data?.harvested}
                    onChangeText={value =>
                      onChangeTextInput(id, 'harvested', value)
                    }
                  />
                ) : null}
              </>
            ) : (
              <NumberInput
                editable={false}
                value={data?.harvested}
                variant="primary"
              />
            )}
            <NumberInputLabel>Colhido</NumberInputLabel>
          </NumberInputContainer>
        )}

        {/* mature | adult | medium */}
        {(id === 0 ||
          id === 1 ||
          id === 2 ||
          id === 3 ||
          id === 4 ||
          id === 5 ||
          id === 7 ||
          id === 8) && (
          <>
            <NumberInputContainer>
              {data?.total !== 0 && !isReviewing ? (
                <NumberInput
                  value={data?.rotten}
                  onChangeText={value => onChangeTextInput(id, 'rotten', value)}
                />
              ) : (data?.rotten !== 0 || data?.total !== 0) && isReviewing ? (
                <NumberInput
                  value={data?.rotten}
                  editable={isReviewing}
                  onChangeText={value => onChangeTextInput(id, 'rotten', value)}
                />
              ) : (
                <NumberInput
                  editable={false}
                  value={data?.rotten}
                  variant="primary"
                  onChangeText={value => onChangeTextInput(id, 'rotten', value)}
                />
              )}
              <NumberInputLabel>Podre</NumberInputLabel>
            </NumberInputContainer>

            <NumberInputContainer>
              {data?.total !== 0 && !isReviewing ? (
                <NumberInput
                  value={data?.rat}
                  onChangeText={value => onChangeTextInput(id, 'rat', value)}
                />
              ) : (data?.rat !== 0 || data?.total !== 0) && isReviewing ? (
                <NumberInput
                  value={data?.rat}
                  editable={isReviewing}
                  onChangeText={value => onChangeTextInput(id, 'rat', value)}
                />
              ) : (
                <NumberInput
                  editable={false}
                  value={data?.rat}
                  variant="primary"
                  onChangeText={value => onChangeTextInput(id, 'rat', value)}
                />
              )}
              <NumberInputLabel>Rato</NumberInputLabel>
            </NumberInputContainer>
          </>
        )}

        {/* medium1 | small | bilro */}
        {(id === 6 || id === 7 || id === 8 || id === 9) && (
          <>
            <NumberInputContainer>
              {data?.total !== 0 && !isReviewing ? (
                <NumberInput
                  value={data?.piece}
                  onBlur={() =>
                    title === '21-42' ? handleConfirm('piece') : null
                  }
                  onChangeText={value => onChangeTextInput(id, 'piece', value)}
                />
              ) : (data?.piece !== 0 || data?.total !== 0) && isReviewing ? (
                <NumberInput
                  value={data?.piece}
                  editable={isReviewing}
                  onChangeText={value => onChangeTextInput(id, 'piece', value)}
                />
              ) : (
                <NumberInput
                  editable={false}
                  value={data?.piece}
                  variant="primary"
                  onChangeText={value => onChangeTextInput(id, 'piece', value)}
                />
              )}
              <NumberInputLabel>Peco</NumberInputLabel>
            </NumberInputContainer>
          </>
        )}

        {/* all (except small) */}
        {(id === 0 ||
          id === 1 ||
          id === 2 ||
          id === 3 ||
          id === 4 ||
          id === 5 ||
          id === 7 ||
          id === 8 ||
          id === 9) && (
          <>
            <NumberInputContainer>
              {data?.total !== 0 && !isReviewing ? (
                <NumberInput
                  value={data?.loss}
                  onBlur={() =>
                    title === '21-42' ? handleConfirm('loss') : null
                  }
                  onChangeText={value => onChangeTextInput(id, 'loss', value)}
                />
              ) : (data?.loss !== 0 || data?.total !== 0) && isReviewing ? (
                <NumberInput
                  value={data?.loss}
                  editable={isReviewing}
                  onChangeText={value => onChangeTextInput(id, 'loss', value)}
                />
              ) : (
                <NumberInput
                  editable={false}
                  value={data?.loss}
                  variant="primary"
                  onChangeText={value => onChangeTextInput(id, 'loss', value)}
                />
              )}
              <NumberInputLabel>Perda</NumberInputLabel>
            </NumberInputContainer>

            <NumberInputContainer>
              {data?.total !== 0 && !isReviewing ? (
                <NumberInput
                  value={data?.witchs_broom}
                  onBlur={() =>
                    title === '21-42' ? handleConfirm('witchs_broom') : null
                  }
                  onChangeText={value =>
                    onChangeTextInput(id, 'witchs_broom', value)
                  }
                />
              ) : (data?.witchs_broom !== 0 || data?.total !== 0) &&
                isReviewing ? (
                <NumberInput
                  value={data?.witchs_broom}
                  editable={isReviewing}
                  onChangeText={value =>
                    onChangeTextInput(id, 'witchs_broom', value)
                  }
                />
              ) : (
                <NumberInput
                  editable={false}
                  value={data?.witchs_broom}
                  variant="primary"
                  onChangeText={value =>
                    onChangeTextInput(id, 'witchs_broom', value)
                  }
                />
              )}
              <NumberInputLabel>V/B</NumberInputLabel>
            </NumberInputContainer>
          </>
        )}

        {id === 6 && (
          <>
            <NumberInputContainer>
              {data?.total !== 0 && !isReviewing ? (
                <NumberInput
                  value={data?.loss}
                  onBlur={() => handleCompute('loss')}
                  onChangeText={value => onChangeTextInput(id, 'loss', value)}
                />
              ) : (data?.loss !== 0 || data?.total !== 0) && isReviewing ? (
                <NumberInput
                  value={data?.loss}
                  editable={isReviewing}
                  onChangeText={value => onChangeTextInput(id, 'loss', value)}
                />
              ) : (
                <NumberInput
                  editable={false}
                  value={data?.loss}
                  variant="primary"
                  onChangeText={value => onChangeTextInput(id, 'loss', value)}
                />
              )}
              <NumberInputLabel>Perda</NumberInputLabel>
            </NumberInputContainer>
          </>
        )}
      </InputGroupContainer>
    </Container>
  );
};

export default CocoaInputBox;
