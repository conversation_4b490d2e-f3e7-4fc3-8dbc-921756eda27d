import styled from 'styled-components/native';
import { Text } from '@components';
import { colors, fonts, spacing } from '@theme';

export const Container = styled.View`
  border-radius: 4px;
  min-height: 60px;
  background-color: ${colors.transparent};
  justify-content: space-between;

  padding: ${spacing.global.spacingMedium}px;
  gap: ${spacing.global.gap * 2}px;
`;

export const TitleContainer = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
`;

export const HealthyFruitsNumberInputContainer = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  gap: ${spacing.global.gap * 2}px;
`;

export const HealthyFruitsNumberInputLabel = styled(Text)`
  font-weight: ${fonts.fontWeight.bold};
  color: ${colors.white};
`;

export const Title = styled(Text)`
  color: ${colors.white};
  font-size: ${fonts.fontSize.large - 2}px;
  font-weight: ${fonts.fontWeight.bold};
`;

export const Separator = styled.View`
  background-color: ${colors.white};
  height: 2px;
  width: 100%;
`;

export const InputGroupContainer = styled.View`
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
`;

export const NumberInputLabel = styled(Text)`
  color: ${colors.white};
  text-align: center;
`;

export const NumberInputContainer = styled.View`
  justify-content: center;
  align-items: center;
  gap: ${spacing.global.gap}px;
`;
