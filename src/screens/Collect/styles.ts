import styled from 'styled-components/native';
import { Text } from '@components';
import { colors, fonts, spacing } from '@theme';

export const CollectContainer = styled.ScrollView.attrs(() => ({
  style: { flex: 1 },
  contentContainerStyle: {
    flexGrow: 1,
    gap: spacing.global.gap * 2,
    paddingBottom: 125,
  },
}))`
  padding-horizontal: ${spacing.global.spacingMedium}px;
`;

export const ScrollViewHeader = styled.View`
  max-height: 120px;
  padding-vertical: ${spacing.global.spacingMedium}px;
  background-color: ${colors.yellow};
  border-radius: 4px;
`;

export const ScrollViewHeaderLabel = styled(Text)`
  font-weight: ${fonts.fontWeight.bold};
  font-size: ${fonts.fontSize.regular}px;
  text-align: center;
`;

export const FooterContainer = styled.View`
  justify-content: center;
  align-items: flex-end;
`;

export const FooterButton = styled.Pressable`
  width: 100%;
  padding-vertical: ${spacing.global.spacingSmall}px;
  padding-horizontal: ${spacing.global.spacingMedium}px;
  background-color: ${colors.primary};
  border-radius: 4px;
`;

export const FooterButtonLabel = styled(Text)`
  color: ${colors.white};
  font-weight: 600;
  text-align: center;
`;
