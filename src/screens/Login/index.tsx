import React, { useEffect, useState } from 'react';
import { isAxiosError } from 'axios';
import { useIsFocused, useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

import { auth, storage } from '@utils';
import { translate } from '@global/i18n';
import { HTTP_CODES } from '@global/constants';
import { useToast } from '@context/ToastContext';
import { RootStackParamList } from '@routes.types';
import { useAuth } from 'src/store/slices/auth/useAuth';
import { Button, CheckBox, Input } from '@components';
import { authService } from '@services/auth/auth.service';
import { realmService } from '@services/realm/realm.service';

import logoImg from '@assets/images/logo.png';
import {
  Container,
  FormContainer,
  LogoImage,
  Title,
  TitleContainer,
} from './styles';
import { IFormValues } from './types';

const Login: React.FC = () => {
  const { show } = useToast();
  const isFocused = useIsFocused();
  const { authenticate } = useAuth();
  const navigation = useNavigation<StackNavigationProp<RootStackParamList>>();
  const [email, setEmail] = useState<string>('');
  const [password, setPassword] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [rememberMe, setRememberMe] = useState<boolean>(false);
  const [secureTextEntry, setSecureTextEntry] = useState<boolean>(true);

  function handleSecureTextEntryChange(): void {
    setSecureTextEntry(!secureTextEntry);
  }

  function handleRememberMe() {
    setRememberMe(!rememberMe);
  }

  function resetForm(): void {
    setEmail('');
    setPassword('');
  }

  async function handleNavigateToPropertyScreen(): Promise<void> {}

  async function handleSubmit(): Promise<void> {
    try {
      setLoading(true);
      const { data, status } = await authService.login({ email, password });
      const { access_token, token_type } = data;

      if (status === HTTP_CODES.OK) {
        authenticate({
          auth: {
            accessToken: access_token,
            tokenType: token_type,
          },
        });
        rememberMe
          ? await storage.setData(
              '@loginCredentials',
              JSON.stringify({ email, password }),
            )
          : await storage.removeData('@loginCredentials');

        await realmService.syncData();
        navigation.navigate('BottomTabStack');
        resetForm();
      }

      setLoading(false);
    } catch (error) {
      console.error(error);
      let message: string = '';

      if (isAxiosError(error)) {
        message =
          (error.response?.status as number) === 401
            ? translate('checkEmailPassword')
            : translate('dataFetchError');
      }

      setLoading(false);
      show({ type: 'error', message });
    }
  }

  async function verifyRememberMe(): Promise<void> {
    const rawLoginCredentials = await storage.getData('@loginCredentials');
    if (rawLoginCredentials) {
      const parsedLoginCredentials = JSON.parse(
        rawLoginCredentials,
      ) as IFormValues;
      setRememberMe(true);

      setEmail(parsedLoginCredentials.email);
      setPassword(parsedLoginCredentials.password);
    }
  }

  useEffect(() => {
    verifyRememberMe();
  }, [isFocused]);

  useEffect(() => {
    const verifiedToken = auth.verifyToken();
    if (verifiedToken) {
      handleNavigateToPropertyScreen();
    }
  }, []);

  return (
    <Container>
      <TitleContainer>
        <LogoImage source={logoImg} />
        <Title>ColetaCacau</Title>
      </TitleContainer>

      <FormContainer>
        <Input
          value={email}
          placeholder={translate('email')}
          onChangeText={setEmail}
          keyboardType="email-address"
        />
        <Input
          value={password}
          placeholder={translate('password')}
          onChangeText={setPassword}
          textContentType="password"
          secureTextEntry={secureTextEntry}
          handleSecureTextEntryChange={handleSecureTextEntryChange}
        />

        <CheckBox
          value={rememberMe}
          label={translate('rememberMe')}
          handleChange={handleRememberMe}
        />

        <Button
          loading={loading}
          onPress={handleSubmit}
          title={translate('login')}
        />
      </FormContainer>
    </Container>
  );
};

export default Login;
