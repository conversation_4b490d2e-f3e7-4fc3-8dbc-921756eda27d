import styled from 'styled-components/native';
import { Text } from '@components';
import { colors, fonts, spacing } from '@theme';

export const Container = styled.View`
  flex: 1;
  justify-content: center;
  padding-horizontal: ${spacing.global.spacingMedium}px;
`;

export const TitleContainer = styled.View`
  justify-content: center;
`;

export const LogoImage = styled.Image`
  resize-mode: contain;
  align-self: center;
  height: 50%;
`;

export const Title = styled(Text)`
  color: ${colors.dark};
  font-size: ${fonts.fontSize.xxlarge}px;
  font-weight: ${fonts.fontWeight.bold};
  text-align: center;
`;

export const FormContainer = styled.View`
  flex: 1;
`;
