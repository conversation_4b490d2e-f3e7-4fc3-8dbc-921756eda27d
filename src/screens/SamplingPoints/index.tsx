import React, { useEffect, useState } from 'react';
import { FlatList } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

import { colors } from '@theme';
import { realm } from '@database/database';
import { RfidReaderLayout } from '@layouts';
import { RootStackParamList } from '@routes.types';
// Store
import { useInfo } from '@store/slices/info/useInfo';
import { useAuth } from '@store/slices/auth/useAuth';
import { useCollect } from '@store/slices/collect/useCollect';
import { useDateControl } from '@store/slices/dateControl/useDateControl';
// Global
import { IProperty } from '@global/types/property.types';
import { IOperationalUnit } from '@global/types/stratum.types';
import { ISamplingPoint } from '@global/types/samplingPoint.types';
import { translate } from '@global/i18n';
// Components
import { AlertValidation, ListItem } from '@components';

import { IProps } from './types';

const SamplingPoints: React.FC<IProps> = ({}) => {
  const { user } = useAuth();
  const { collect, initializeCurrentCollect } = useCollect();
  const { propertyId, stratumId, setSamplingPointId } = useInfo();
  const { collectControl, setLastFruitCollectDate } = useDateControl();
  const navigation = useNavigation<StackNavigationProp<RootStackParamList>>();

  const [property, setProperty] = useState<IProperty>();
  const [operationalUnit, setOperationalUnit] = useState<IOperationalUnit>();
  const [samplingPoints, setSamplingPoints] = useState<Array<ISamplingPoint>>();
  const [showAlertValidation, setShowAlertValidation] =
    useState<boolean>(false);
  const [samplingPointCurrentCollectIds, setSamplingPointCurrentCollectIds] =
    useState<Array<number>>([]);
  const [samplingPointVisitedIds, setSamplingPointVisitedIds] = useState<
    Array<number>
  >([]);

  function getProperty(): IProperty {
    const properties = realm.objects<Realm.Object & IProperty>('Property');
    return properties.filtered(`id=${propertyId}`)[0];
  }

  function getOperationalUnit(): IOperationalUnit {
    const rawOperationalUnit = realm.objects<Realm.Object & IOperationalUnit>(
      'Strata',
    );
    return rawOperationalUnit.filtered(
      `id=${stratumId} && sampling_points.@count>0`,
    )[0];
  }

  function getSamplingPoint(samplingPointId: number): ISamplingPoint {
    const rawSamplingPoints = realm.objects<Realm.Object & ISamplingPoint>(
      'SamplingPoint',
    );
    return rawSamplingPoints.filtered(`id=${samplingPointId}`)[0];
  }

  function checkCollectStatus() {
    const today = new Date();
    const samplingPointWasVisitedRecentlyList = collectControl
      .map(c => {
        const timeDiff = Math.abs(
          new Date(c.previous as any).getTime() - today.getTime(),
        );
        const diffDays = Math.ceil(timeDiff / (1000 * 3600 * 24));
        if (diffDays < 18) {
          return c.sampling_point_id;
        }
      })
      .filter(c => c !== undefined);
    setSamplingPointVisitedIds(samplingPointWasVisitedRecentlyList);

    const _currentCollect: number[] = [];
    collect.map(c => {
      c.sampling_points.map(sp => _currentCollect.push(sp.sampling_point_id));
    });
    setSamplingPointCurrentCollectIds(_currentCollect);
  }

  function samplingPointHasAlreadyBeenVisited(samplingPointId: number) {
    const lastCollect = collectControl
      .map(c => {
        if (c.sampling_point_id === samplingPointId) {
          return c;
        }
      })
      .filter(c => c !== undefined);

    const today = new Date();
    const timeDiff = Math.abs(
      new Date(lastCollect[0]?.previous as any).getTime() - today.getTime(),
    );
    const diffDays = Math.ceil(timeDiff / (1000 * 3600 * 24));
    return diffDays < 18;
  }

  function checkSamplingPointStatus(id: number, type: 'color' | 'background') {
    if (samplingPointVisitedIds.includes(id)) {
      if (type === 'background') {
        return colors.green;
      }
      return colors.white;
    }

    if (samplingPointCurrentCollectIds.includes(id)) {
      if (type === 'background') {
        return colors.yellow;
      }
      return colors.dark;
    } else {
      if (type === 'background') {
        return colors.deepPetrolBlue;
      }
      return colors.white;
    }
  }

  function _keyExtractor(item: ISamplingPoint, index: number) {
    return `${item.label}-${index}`;
  }

  function _renderItem({ item }: { item: ISamplingPoint }) {
    const color = checkSamplingPointStatus(item.id, 'color');
    const backgroundColor = checkSamplingPointStatus(item.id, 'background');

    const handlePress = () => {
      const hasAlreadyBeenVisited = samplingPointHasAlreadyBeenVisited(item.id);
      if (hasAlreadyBeenVisited) {
        setShowAlertValidation(true);
      } else {
        setSamplingPointId({ samplingPointId: item.id });
        initializeCurrentCollect({ stratumId, samplingPointId: item.id });
        navigation.navigate('Trees', {
          subtitle: `${property?.name} - ${
            property?.homogeneous_areas[0].label
          } - ${operationalUnit?.label}\n${translate('samplingPoint')} ${
            item.label
          } - ${translate('pa')} ${item.label}`,
          title: `${translate('trees')}`,
        });
      }
    };

    return (
      <ListItem
        title={`${translate('samplingPoint')} ${item.label} - ${translate(
          'pa',
        )}${item.label}`}
        backgroundColor={backgroundColor}
        color={color}
        onPress={handlePress}
      />
    );
  }

  async function updateLastCollectDate(samplingPointId: number) {
    try {
      const filteredSamplingPoint = getSamplingPoint(samplingPointId);

      let date = null;
      let previousDate = null;
      if (!filteredSamplingPoint.lastVisit) {
        date = new Date();
        date.setDate(date.getDate() - 18);
        previousDate = new Date(
          `${date.getFullYear}-${date.getMonth() + 1}-${date.getDate()}`,
        );
      } else {
        date = filteredSamplingPoint.lastVisit as string;
        previousDate = new Date(date.split(' ')[0]);
      }

      let lastCollectRegister = [];
      if (!(collectControl.length > 0)) {
        setLastFruitCollectDate({ samplingPointId, date: previousDate });
      } else {
        const findSamplingPoint = collectControl.findIndex(
          value => value.sampling_point_id === samplingPointId,
        );
        if (findSamplingPoint !== -1) {
          lastCollectRegister = collectControl
            .map(value => {
              if (value.sampling_point_id === samplingPointId) {
                return value;
              }
            })
            .filter(value => value !== undefined);

          if (
            lastCollectRegister[0]?.previous &&
            previousDate.getDate() >
              new Date(lastCollectRegister[0]?.previous).getTime()
          ) {
            setLastFruitCollectDate({ samplingPointId, date: previousDate });
          }
        } else {
          setLastFruitCollectDate({ samplingPointId, date: previousDate });
        }
      }
    } catch (error) {
      console.error(error);
    }
  }

  useEffect(() => {
    const filteredProperty = getProperty();
    const filteredOperationalUnit = getOperationalUnit();

    let _samplingPoints: ISamplingPoint[] = [];
    filteredOperationalUnit.sampling_points.map(sp => {
      if (sp.users_ids.length > 0) {
        sp.users_ids.map(u => {
          if (u.user_id === user?.id) {
            _samplingPoints.push(sp);
          }
        });
      }
    });

    _samplingPoints.forEach(sp => {
      updateLastCollectDate(sp.id);
    });

    setProperty(filteredProperty);
    setOperationalUnit(filteredOperationalUnit);
    setSamplingPoints(_samplingPoints);

    checkCollectStatus();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <RfidReaderLayout>
      <AlertValidation
        visible={showAlertValidation}
        title={'Atenção!'}
        subtitle={'Ponto de amostragem já visitado'}
        message={'Aguarde até a próxima data de coleta'}
        onClose={() => setShowAlertValidation(false)}
      />

      <FlatList
        data={samplingPoints}
        keyExtractor={_keyExtractor}
        renderItem={_renderItem}
      />
    </RfidReaderLayout>
  );
};

export default SamplingPoints;
