import styled from 'styled-components/native';
import { colors, spacing } from '@theme';
import { Text } from '@components';

export const Container = styled.View`
  flex: 1;
`;

export const Row = styled.View<{ backgroundColor: string }>`
  background-color: ${({ backgroundColor }) => backgroundColor};
  flex-direction: row;
  align-items: center;
  justify-content: space-between;

  padding-vertical: ${spacing.global.spacingMedium}px;
  padding-horizontal: ${spacing.global.spacingLarge}px;
`;

export const RowLabel = styled(Text)<{ color: string }>`
  color: ${({ color }) => color};
`;

export const FooterContainer = styled.View`
  justify-content: center;
  align-items: flex-end;

  padding-vertical: ${spacing.global.spacingMedium}px;
  padding-horizontal: ${spacing.global.spacingLarge}px;
`;

export const FooterButton = styled.Pressable`
  width: 100%;
  padding-vertical: ${spacing.global.spacingSmall}px;
  padding-horizontal: ${spacing.global.spacingMedium}px;
  background-color: ${colors.primary};
  border-radius: 4px;
`;

export const FooterButtonLabel = styled(Text)`
  color: ${colors.white};
  font-weight: 600;
  text-align: center;
`;
