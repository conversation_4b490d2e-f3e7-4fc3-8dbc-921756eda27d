import React, { useMemo, useState } from 'react';
import { FlatList } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

import { area } from '@utils';
import { colors, misc } from '@theme';
import { CheckBox } from '@components';
import { RootStackParamList } from '@routes.types';
import { useInfo } from '@store/slices/info/useInfo';
import { usePractice } from '@store/slices/practice/usePractice';
import {
  IAreaInfo,
  IPracticeDataStage1,
} from '@global/types/visitInformation.types';

import {
  Container,
  FooterButton,
  FooterButtonLabel,
  FooterContainer,
  Row,
  RowLabel,
} from './styles';

const PracticeData: React.FC = () => {
  const { saveCurrentPracticeArea } = usePractice();
  const navigation = useNavigation<StackNavigationProp<RootStackParamList>>();
  const { homogeneousAreaId, propertyId } = useInfo();
  const [practiceData, setPracticeData] = useState<IPracticeDataStage1>({
    flowering: false,
    refoliation: false,
    top: false,
    pruned: false,
    mowing: false,
    weeding: false,
    grated: false,
    renewed: false,
    unbounded: false,
    fertilized: false,
    pulverized: false,
    brown_rot: false,
    wind: false,
    drought: false,
    rain: false,
    rat: false,
    flood: false,
    insect: false,
    absence_of_shadow: false,
    excess_shade: false,
  } as IPracticeDataStage1);
  const infoArea: Array<IAreaInfo> = useMemo(() => {
    const _infoArea = area.getAreaInfo((info: IAreaInfo) => info.id > 3);
    return _infoArea;
  }, []);

  function handleCheckSelected(key: keyof IPracticeDataStage1): void {
    setPracticeData(prevState => ({ ...prevState, [key]: !practiceData[key] }));
  }

  function checkSelected(data: keyof IPracticeDataStage1): boolean {
    const { drought, rain, absence_of_shadow, excess_shade } = practiceData;
    if (
      (drought && data === 'rain') ||
      (drought && data === 'flood') ||
      (rain && data === 'drought') ||
      (absence_of_shadow && data === 'excess_shade') ||
      (excess_shade && data === 'absence_of_shadow')
    ) {
      return true;
    }
    return false;
  }

  function _renderItem({ item }: { item: IAreaInfo }) {
    const { id, label, key } = item;
    const isEven = id % 2;
    const defaultColor = isEven ? colors.white : colors.primary;
    return (
      <Row backgroundColor={isEven ? colors.primary : colors.greenLighter}>
        <RowLabel color={defaultColor}>{label}</RowLabel>
        <CheckBox
          disabled={checkSelected(key)}
          value={practiceData[key] as boolean}
          handleChange={() => handleCheckSelected(key)}
          borderColor={defaultColor}
          backgroundColor={defaultColor}
          iconColor={isEven ? 'primary' : 'white'}
        />
      </Row>
    );
  }

  function _keyExtractor(item: IAreaInfo) {
    return `${item.id}`;
  }

  function _renderFooter() {
    function handleNext(): void {
      saveCurrentPracticeArea({
        propertyId,
        homogeneousAreaId,
        practice: { ...practiceData } as any,
      });
      navigation.navigate('PracticeAreaData');
    }

    return (
      <FooterContainer>
        <FooterButton onPress={handleNext} style={misc.effects.boxShadow}>
          <FooterButtonLabel>{'Próximo'}</FooterButtonLabel>
        </FooterButton>
      </FooterContainer>
    );
  }

  return (
    <Container>
      <FlatList
        data={infoArea}
        keyExtractor={_keyExtractor}
        renderItem={_renderItem}
        contentContainerStyle={{ paddingBottom: 100 }}
        ListFooterComponent={_renderFooter}
      />
    </Container>
  );
};

export default PracticeData;
