import { IHomogeneousArea } from './homogeneousArea.types';
import { ISamplingPoint } from './samplingPoint.types';

interface ITreeRange {
  max_trees: number;
  min_trees: number;
}

interface IOperationalUnit {
  id: number;
  label: string;
  total_sampling_points: number;
  sampling_points: Array<ISamplingPoint>;
  stratum_tree_range: ITreeRange;
  homogeneous_areas: Array<IHomogeneousArea>;
}

export type { IOperationalUnit };
