import {
  IAdultStage1,
  IA<PERSON>lt<PERSON>tage2,
  <PERSON><PERSON><PERSON><PERSON>,
  IMatureStage1,
  IMatureStage2,
  IMatureStage3,
  IMatureStage4,
  IMediumStage1,
  IMediumStage2,
  IMediumStage3,
  ISmall,
} from './cocoa.types';

interface IHomogeneousArea {
  homogeneous_area_id: number;
  visit_information: {};
}

interface ISamplingPoint {
  sampling_point_id: number;
  trees: Array<any>;
}

interface ICollectData {
  stratum_id?: number;
  sampling_point_id?: number;
  sampling_points: Array<ISamplingPoint>;
}

interface ICollect {
  bobbin: IBilro;
  bobbinPV: IBilro;
  small: ISmall;
  medium: IMediumStage1;
  medium2: IMediumStage2;
  medium3: IMediumStage3;
  adult: IAdultStage1;
  adult2: IAdultStage2;
  mature: IMatureStage1;
  mature2: IMatureStage2;
  mature3: IMatureStage3;
  mature4: IMatureStage4;
}

interface IPracticeData {
  property_id?: number;
  homogeneous_area: Array<IHomogeneousArea>;
}

interface IPracticeArea2 {
  flowering: number;
  refoliation: number;
  top: number;
  note?: string;
  date?: Date;
}

interface ICheckQuantityFunc {
  isError: boolean;
  quantity?: number;
  title?: string;
  message?: string;
}

interface ICollectControl {
  stratum_id?: number;
  sampling_point_id?: number;
  sampling_points: Array<ISamplingPoint>;
  previous?: Date;
}

export type {
  IHomogeneousArea,
  ICollectData,
  ICollect,
  IPracticeData,
  IPracticeArea2,
  ICheckQuantityFunc,
  ICollectControl,
};
