import { IOperationalUnit } from './stratum.types';
import { ITree } from './trees.types';

interface IUserId {
  sampling_point_id: number;
  user_id: number;
}

interface ISamplingPoint {
  id: number;
  harvest: number;
  ini_period: number;
  label: string;
  lastVisit: Date | string;
  year: number;
  strata: Array<IOperationalUnit>;
  trees: Array<ITree>;
  users_ids: Array<IUserId>;
}

export type { ISamplingPoint };
