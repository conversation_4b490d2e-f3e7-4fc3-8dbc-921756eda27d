import {
  IAdultStage1,
  IAdultStage2,
  <PERSON><PERSON><PERSON><PERSON>,
  IMatureStage1,
  IMatureStage2,
  IMatureStage3,
  IMatureStage4,
  IMediumStage1,
  IMediumStage2,
  IMediumStage3,
  ISmall,
} from './cocoa.types';
import { ITree } from './trees.types';
import { IVisitInformation } from './visitInformation.types';

interface ITreeVisit {
  id: number;
  tree: Array<ITree>;
  bobbin: IBilro;
  bobbinPV: IBilro;
  small: ISmall;
  medium: IMediumStage1;
  medium2: IMediumStage2;
  medium3: IMediumStage3;
  adult: IAdultStage1;
  adult2: IAdultStage2;
  mature: IMatureStage1;
  mature2: IMatureStage2;
  mature3: IMatureStage3;
  mature4: IMatureStage4;
  total_good?: number;
  total_harvested?: number;
  total_loss?: number;
  total_piece?: number;
  total_rat?: number;
  total_rotten?: number;
  total_wb?: number;
  visit_information: IVisitInformation;
  created_at?: string;
  date: string;
}

export type { ITreeVisit };
