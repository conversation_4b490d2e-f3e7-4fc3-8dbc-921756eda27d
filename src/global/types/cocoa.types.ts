type FruitStages =
  | 'bobbin'
  | 'bobbinPV'
  | 'small'
  | 'medium'
  | 'medium2'
  | 'medium3'
  | 'adult'
  | 'adult2'
  | 'mature'
  | 'mature2'
  | 'mature3'
  | 'mature4';

interface IBilro {
  total: number;
}

interface ISmall {
  total: number;
  loss: number;
  piece: number;
  witchs_broom: number;
}

interface IMediumStage1 {
  total: number;
  loss: number;
  piece: number;
}

interface IMediumStage2 {
  total: number;
  rotten: number;
  rat: number;
  witchs_broom: number;
  loss: number;
  piece: number;
}

interface IMediumStage3 {
  total: number;
  harvested: number;
  rotten: number;
  rat: number;
  witchs_broom: number;
  loss: number;
  piece: number;
}

interface IAdultStage1 {
  total: number;
  harvested: number;
  rotten: number;
  rat: number;
  witchs_broom: number;
  loss: number;
}

interface IAdultStage2 {
  total: number;
  harvested: number;
  rotten: number;
  rat: number;
  witchs_broom: number;
  loss: number;
}

interface IMatureStage1 {
  total: number;
  harvested: number;
  rotten: number;
  rat: number;
  witchs_broom: number;
  loss: number;
}

interface IMatureStage2 {
  total: number;
  harvested: number;
  rotten: number;
  rat: number;
  witchs_broom: number;
  loss: number;
}

interface IMatureStage3 {
  total: number;
  harvested: number;
  rotten: number;
  rat: number;
  witchs_broom: number;
  loss: number;
}

interface IMatureStage4 {
  total: number;
  harvested: number;
  rotten: number;
  rat: number;
  witchs_broom: number;
  loss: number;
}

export type {
  FruitStages,
  IBilro,
  ISmall,
  IMediumStage1,
  IMediumStage2,
  IMediumStage3,
  IAdultStage1,
  IAdultStage2,
  IMatureStage1,
  IMatureStage2,
  IMatureStage3,
  IMatureStage4,
};
