import { IRfid } from './rfid.types';
import { ISamplingPoint } from './samplingPoint.types';
import { IVisitInformation } from './visitInformation.types';

interface ITree {
  id: number;
  label: string;
  sampling_point_id?: number;
  tree_visits: Array<IVisitInformation>;
  sampling_points: Array<ISamplingPoint>;
  rfid?: IRfid;
  alternative_label?: string;
  status: number;
}

export type { ITree };
