import { IHomogeneousArea } from './collect.types';

interface IVisitInformation {
  id: number;
  property_id?: number;
  absence_of_shadow: boolean;
  brown_rot: boolean;
  date: string;
  drought: boolean;
  excess_shade: boolean;
  fertilized: boolean;
  flood: boolean;
  flowering: boolean | string | number;
  grated: boolean;
  homogeneous_area: Array<IHomogeneousArea>;
  insect: boolean;
  mowing: boolean;
  note?: string;
  pruned: boolean;
  pulverized: boolean;
  rain: boolean;
  rat: boolean;
  refoliation: boolean | string | number;
  renewed: boolean;
  top: boolean | string | number;
  treeVisit: Array<number>;
  unbounded: boolean;
  weeding: boolean;
  wind: boolean;
}

interface IPracticeDataStage1
  extends Omit<
    IVisitInformation,
    'homogeneous_area' | 'id' | 'date' | 'treeVisit'
  > {}

interface IPracticeDataStage2
  extends Pick<
    IVisitInformation,
    'flowering' | 'refoliation' | 'top' | 'date' | 'note'
  > {}

interface IAreaInfo {
  id: number;
  label: string;
  key: keyof IPracticeDataStage1;
}

export type {
  IVisitInformation,
  IPracticeDataStage1,
  IPracticeDataStage2,
  IAreaInfo,
};
