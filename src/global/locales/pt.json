{"login": "Entrar", "user": "<PERSON><PERSON><PERSON><PERSON>", "email": "E-mail", "password": "<PERSON><PERSON>", "checkEmailPassword": "Verifique se o email e senha fornecidos estão corretos", "dataFetchError": "Não foi possível trazer os dados. Tente mais tarde", "rememberMe": "<PERSON><PERSON><PERSON><PERSON>me", "properties": "<PERSON><PERSON><PERSON><PERSON>", "homogeneousAreas": "<PERSON><PERSON><PERSON>", "homogeneousArea": "<PERSON><PERSON>", "samplingPoints": "<PERSON><PERSON>", "trees": "<PERSON><PERSON><PERSON><PERSON>", "ha": "AH", "operationalUnit": "Unidade Operacional", "operationalUnits": "Unidades Operacionais", "ou": "UO", "about": "Sobre", "home": "Início", "profile": "Perfil", "sentenceAbout": "Aplicativo desenvolvido pelo Projeto de Extensão Grupo Imagem e Ação em parceria com a Comissão Executiva do Plano da Lavoura Cacaueira - CEPLAC para coleta de dados da lavoura de cacau.\n\nDesenvolvido pelos al<PERSON><PERSON>, <PERSON><PERSON> e Christian Menezes com orientação da Professora Marta Magda Dornelles e colaboração de Lindolfo Pereira.", "cultivationData": "Dados do Cultivo do Cacau", "name": "Nome", "cpf": "CPF", "birthDate": "Data de nascimento", "status": "Status", "active": "Ativo", "inactive": "Inativo", "collectPracticeData": "Coletar <PERSON> da Prática", "practiceData": "Dados da Prática", "samplingPoint": "<PERSON><PERSON>", "pa": "PA", "farm": "Fazenda", "tree": "<PERSON><PERSON><PERSON><PERSON>", "collection": "Cole<PERSON>", "completed": "Conclu<PERSON><PERSON>", "pending": "Pendente"}