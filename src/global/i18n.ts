import { findBestLanguageTag } from 'react-native-localize';
import enLang from './locales/en.json';
import ptLang from './locales/pt.json';

const defaultLanguage = 'pt';

export type LangKeys = keyof typeof ptLang;

const languages: Record<string, any> = {
  en: enLang,
  pt: ptLang,
};

/**
 * Função para obter o idioma do dispositivo.
 *
 * @returns O idioma do dispositivo.
 */
function getDeviceLanguage() {
  const { languageTag } = findBestLanguageTag(Object.keys(languages)) || {
    languageTag: defaultLanguage,
  };
  return languageTag;
}

/**
 * Função para obter a tradução com base no idioma atual.
 *
 * @param key - A chave da tradução.
 * @param params - Parâmetros opcionais para substituir na tradução.
 * @returns A tradução correspondente à chave e idioma atual.
 */
function translate(key: LangKeys, params?: Record<string, string>): string {
  const language = getDeviceLanguage();
  const translation = languages[language] || languages[defaultLanguage];
  let translatedText = translation[key] || languages[defaultLanguage][key];

  if (params) {
    Object.keys(params).forEach(paramKey => {
      const paramValue = params[paramKey];
      translatedText = translatedText.replace(`{${paramKey}}`, paramValue);
    });
  }

  return translatedText;
}

export { translate };
