import {
  IAreaInfo,
  IVisitInformation,
} from '@global/types/visitInformation.types';
import { ITreeVisit } from './types/treeVisit.types';

export const COLLECT_INTERVAL_DAYS = 21;

export const AREA_INFO: Array<IAreaInfo> = [
  { id: 1, label: 'Floração', key: 'flowering' },
  { id: 2, label: 'Refoliação', key: 'refoliation' },
  { id: 3, label: 'Copa', key: 'top' },
  { id: 4, label: 'Podada', key: 'pruned' },
  { id: 5, label: 'Roçada', key: 'mowing' },
  { id: 6, label: 'Capinada', key: 'weeding' },
  { id: 7, label: '<PERSON>lea<PERSON>', key: 'grated' },
  { id: 8, label: 'Renovada', key: 'renewed' },
  { id: 9, label: 'Desbrotada', key: 'unbounded' },
  { id: 10, label: 'Adubada', key: 'fertilized' },
  { id: 11, label: 'Pulverizada', key: 'pulverized' },
  { id: 12, label: 'C.P. Parda', key: 'brown_rot' },
  { id: 13, label: 'Vento', key: 'wind' },
  { id: 14, label: 'Estiagem', key: 'drought' },
  { id: 15, label: 'Chuva', key: 'rain' },
  { id: 16, label: 'Ratos', key: 'rat' },
  { id: 17, label: 'Enchente', key: 'flood' },
  { id: 18, label: 'Insetos', key: 'insect' },
  { id: 19, label: 'Ausencia de Sombra', key: 'absence_of_shadow' },
  { id: 20, label: 'Excesso de Sombra', key: 'excess_shade' },
];

export const FRUIT_DICT = {
  bilro: 'bobbin',
  bilroPV: 'bobbinPV',
  pequeno: 'small',
  medio: 'medium',
  medio2: 'medium2',
  medio3: 'medium3',
  adulto: 'adult',
  adulto2: 'adult2',
  maduro: 'mature',
  maduro2: 'mature2',
  maduro3: 'mature3',
  maduro4: 'mature4',
  peco: 'piece',
  perda: 'loss',
  vb: 'witchs_broom',
  total: 'total',
  rato: 'rat',
  podre: 'rotten',
  colhido: 'harvested',
};

export const COCOA_TYPE = [
  { key: 'Maduro', id: 0 },
  { key: 'Adulto', id: 1 },
  { key: 'Medio', id: 2 },
  { key: 'Pequeno', id: 3 },
  { key: 'Bilro', id: 4 },
];

export const HTTP_CODES = {
  OK: 200,
  UNAUTHORIZED: 401,
  NOT_FOUND: 404,
};

export const TREE_VISIT_INITIAL_STATE: ITreeVisit = {
  id: 0,
  tree: [],
  visit_information: {} as IVisitInformation,
  date: '',
  total_good: 0,
  total_harvested: 0,
  total_loss: 0,
  total_piece: 0,
  total_rat: 0,
  total_rotten: 0,
  total_wb: 0,
  bobbin: { total: 0 },
  bobbinPV: { total: 0 },
  small: { total: 0, loss: 0, piece: 0, witchs_broom: 0 },
  medium: { total: 0, loss: 0, piece: 0 },
  medium2: {
    total: 0,
    rotten: 0,
    rat: 0,
    witchs_broom: 0,
    loss: 0,
    piece: 0,
  },
  medium3: {
    total: 0,
    harvested: 0,
    rotten: 0,
    rat: 0,
    witchs_broom: 0,
    loss: 0,
    piece: 0,
  },
  adult: {
    total: 0,
    harvested: 0,
    rotten: 0,
    rat: 0,
    witchs_broom: 0,
    loss: 0,
  },
  adult2: {
    total: 0,
    harvested: 0,
    rotten: 0,
    rat: 0,
    witchs_broom: 0,
    loss: 0,
  },
  mature: {
    total: 0,
    harvested: 0,
    rotten: 0,
    rat: 0,
    witchs_broom: 0,
    loss: 0,
  },
  mature2: {
    total: 0,
    harvested: 0,
    rotten: 0,
    rat: 0,
    witchs_broom: 0,
    loss: 0,
  },
  mature3: {
    total: 0,
    harvested: 0,
    rotten: 0,
    rat: 0,
    witchs_broom: 0,
    loss: 0,
  },
  mature4: {
    total: 0,
    harvested: 0,
    rotten: 0,
    rat: 0,
    witchs_broom: 0,
    loss: 0,
  },
};

export const SUPPORTED_DEVICES = {
  rfidReader125KHz: { vendorId: 65535, productId: 53 },
  rfidReader13MHz: { vendorId: 99999, productId: 99 },
};

export const COMPATIBLE_VENDOR_IDS = Object.values(SUPPORTED_DEVICES).map(
  device => device.vendorId,
);
