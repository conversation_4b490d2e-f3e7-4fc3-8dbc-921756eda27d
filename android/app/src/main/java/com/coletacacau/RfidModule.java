package com.coletacacau;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.hardware.input.InputManager;
import android.hardware.usb.UsbDevice;
import android.hardware.usb.UsbDeviceConnection;
import android.hardware.usb.UsbManager;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.InputDevice;
import android.view.InputEvent;
import android.view.KeyEvent;
import android.view.View;
import android.view.inputmethod.InputMethodManager;

import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.ReadableArray;
import com.facebook.react.modules.core.DeviceEventManagerModule;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

public class RfidModule extends ReactContextBaseJavaModule implements InputManager.InputDeviceListener, View.OnKeyListener {

    private static final String TAG = "RfidModule";
    private UsbManager usbManager;
    private UsbDevice usbDevice;
    private UsbDeviceConnection usbDeviceConnection;
    private BroadcastReceiver broadcastReceiver;
    private InputManager inputManager;
    private List<Integer> vendorIDs = new ArrayList<>();
    private StringBuilder tagData = new StringBuilder();

    public RfidModule(ReactApplicationContext reactApplicationContext) {
        super(reactApplicationContext);
        usbManager = (UsbManager) reactApplicationContext.getSystemService(Context.USB_SERVICE);
        inputManager = (InputManager) reactApplicationContext.getSystemService(Context.INPUT_SERVICE);

        broadcastReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                String action = intent.getAction();

                if (UsbManager.ACTION_USB_DEVICE_ATTACHED.equals(action)) {
                    UsbDevice device = intent.getParcelableExtra(UsbManager.EXTRA_DEVICE);
                    if (device != null && isRFIDReader(device)) {
                        usbDevice = device;
                        usbDeviceConnection = usbManager.openDevice(device);
                        sendEvent("onDeviceConnected", "RFID reader connected");
                        startListeningForKeyEvents();
                    }
                } else if (UsbManager.ACTION_USB_DEVICE_DETACHED.equals(action)) {
                    UsbDevice device = intent.getParcelableExtra(UsbManager.EXTRA_DEVICE);
                    if (device != null && device.equals(usbDevice)) {
                        stopListeningForKeyEvents();
                        closeConnection();
                        usbDevice = null;
                        sendEvent("onDeviceDisconnected", "RFID reader disconnected");
                    }
                }
            }
        };

        IntentFilter filter = new IntentFilter();
        filter.addAction(UsbManager.ACTION_USB_DEVICE_ATTACHED);
        filter.addAction(UsbManager.ACTION_USB_DEVICE_DETACHED);
        reactApplicationContext.registerReceiver(broadcastReceiver, filter);

        inputManager.registerInputDeviceListener(this, new Handler(Looper.getMainLooper()));
    }

    @Override
    public String getName() {
        return "RfidModule";
    }

    @ReactMethod
    public void initialize(ReadableArray compatibleVendorIDsArray) {
        vendorIDs.clear();
        for (int i = 0; i < compatibleVendorIDsArray.size(); i++) {
            vendorIDs.add(compatibleVendorIDsArray.getInt(i));
        }

        HashMap<String, UsbDevice> deviceList = usbManager.getDeviceList();
        Iterator<UsbDevice> deviceIterator = deviceList.values().iterator();
        while (deviceIterator.hasNext()) {
            UsbDevice device = deviceIterator.next();
            if (isRFIDReader(device)) {
                usbDevice = device;
                usbDeviceConnection = usbManager.openDevice(device);
                sendEvent("onDeviceConnected", "RFID reader connected");
                startListeningForKeyEvents();
                return;
            }
        }
        sendEvent("onDeviceError", "No RFID reader found");
    }

    @ReactMethod
    public void reconnectDevice() {
        if (usbDevice == null) {
            HashMap<String, UsbDevice> deviceList = usbManager.getDeviceList();
            Iterator<UsbDevice> deviceIterator = deviceList.values().iterator();
            while (deviceIterator.hasNext()) {
                UsbDevice device = deviceIterator.next();
                if (isRFIDReader(device)) {
                    usbDevice = device;
                    usbDeviceConnection = usbManager.openDevice(device);

                    if (usbDeviceConnection != null) {
                        startListeningForKeyEvents();
                        sendEvent("onDeviceConnected", "RFID reader reconnected");
                    } else {
                        sendEvent("onDeviceError", "Failed to reconnect RFID reader");
                    }
                    return;
                }
            }
            sendEvent("onDeviceError", "No RFID reader found to reconnect");
        } else {
            sendEvent("onDeviceError", "Device is already connected");
        }
    }

    @ReactMethod
    public void disconnectDevice() {
        if (usbDevice != null && usbDeviceConnection != null) {
            stopListeningForKeyEvents();
            closeConnection();
            usbDevice = null;
            usbDeviceConnection = null;
            sendEvent("onDeviceDisconnected", "RFID reader manually disconnected");
        } else {
            sendEvent("onDeviceError", "No device connected to disconnect");
        }
    }

    @ReactMethod
    public void showKeyboard() {
        InputMethodManager imm = (InputMethodManager) getReactApplicationContext()
                .getSystemService(Context.INPUT_METHOD_SERVICE);
        if (imm != null) {
            imm.toggleSoftInput(InputMethodManager.SHOW_FORCED, 0);
        }
    }

    @ReactMethod
    public void hideKeyboard() {
        InputMethodManager imm = (InputMethodManager) getReactApplicationContext()
                .getSystemService(Context.INPUT_METHOD_SERVICE);
        if (imm != null && getCurrentActivity().getCurrentFocus() != null) {
            imm.hideSoftInputFromWindow(getCurrentActivity()
                    .getCurrentFocus().getWindowToken(), 0);
        }
    }

    @Override
    public boolean onKey(View v, int keyCode, KeyEvent event) {
        sendEvent("onTagRead", "666");
        if (event.getAction() == KeyEvent.ACTION_DOWN) {
            char pressedKey = (char) event.getUnicodeChar();
            sendEvent("onTagRead", "123456789");
            if (pressedKey != 0) {
                tagData.append(pressedKey);

                Log.d(TAG, tagData.toString());

                String tag = tagData.toString().trim();
                sendEvent("onTagRead", tag);
                tagData.setLength(0);
            }
            return true;
        }
        return false;
    }

    private boolean isRFIDReader(UsbDevice device) {
        return vendorIDs.contains(device.getVendorId());
    }

    private boolean isHIDKeyboard(InputDevice device) {
        return (device.getSources() & InputDevice.SOURCE_KEYBOARD) == InputDevice.SOURCE_KEYBOARD;
    }

    private String readFromUsbDevice(UsbDeviceConnection connection, UsbDevice device) {
        byte[] buffer = new byte[64];
        int bytesRead = connection.bulkTransfer(device.getInterface(0)
                .getEndpoint(0), buffer, buffer.length, 1000);

        if (bytesRead > 0) {
            return new String(buffer, 0, bytesRead);
        }

        return null;
    }

    private void sendEvent(String eventName, String message) {
        getReactApplicationContext()
                .getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter.class)
                .emit(eventName, message);
    }

    private void startListeningForKeyEvents() {
        sendEvent("onKeyListenerStart", "Listening for key events");
    }

    private void stopListeningForKeyEvents() {
        sendEvent("onKeyListenerStop", "Stopped listening for key events");
    }

    private void closeConnection() {
        if (usbDeviceConnection != null) {
            usbDeviceConnection.close();
            usbDeviceConnection = null;
        }
    }

    @Override
    public void onInputDeviceAdded(int deviceId) {
        InputDevice device = inputManager.getInputDevice(deviceId);
        if (device != null && isHIDKeyboard(device)) {
            View view = new View(getReactApplicationContext());
            view.setOnKeyListener(this);
            sendEvent("onDeviceConnected", "HID keyboard input device added");
        }
    }

    @Override
    public void onInputDeviceRemoved(int deviceId) {
        sendEvent("onDeviceDisconnected", "RFID reader input device removed");
    }

    @Override
    public void onInputDeviceChanged(int deviceId) {
    }

}
