platform :android do
  desc "Build Artifact"
  lane :build do
    sh "yarn"

    gradle(
      project_dir: "./android/",
      task: "assemble",
      build_type: "Release",
      properties: {
        "versionCode": ENV.fetch('BUILD_NUMBER'),
        "versionName": ENV.fetch('VERSION_NUMBER')
      }
    )
  end

  desc "Upload to App Center"
  lane :deploy_to_appcenter do |params|
    apk_file_path = "./android/app/build/outputs/apk/release/app-release.apk"

    appcenter_upload(
      owner_name: ENV.fetch("OWNER_NAME"),
      app_name: ENV.fetch("APP_NAME"),
      api_token: ENV.fetch("API_TOKEN"),
      file: apk_file_path,
      notify_testers: true,
      destinations: '*'
    )
  end

  desc "Build & Deploy"
  lane :deploy do 
    build
    deploy_to_appcenter(
      environment: "DEV"
    )
  end
end
