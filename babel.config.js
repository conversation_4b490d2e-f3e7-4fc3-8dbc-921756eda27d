module.exports = {
  presets: ['module:metro-react-native-babel-preset'],
  plugins: [
    [
      'module:react-native-dotenv',
      {
        envName: 'APP_ENV',
        moduleName: '@env',
        path: '.env',
      },
    ],
    'babel-plugin-styled-components',
    [
      'module-resolver',
      {
        extensions: ['.js', '.jsx', '.json', '.ts', '.tsx'],
        root: ['.'],
        alias: {
          '@assets': './src/assets',
          '@components': './src/components/index.ts',
          '@screens': './src/screens',
          '@theme': './src/theme/index.ts',
          '@utils': './src/utils/index.ts',
          '@global': './src/global',
          '@context': './src/context',
          '@routes': './src/routes/index.routes.tsx',
          '@routes.types': './src/routes/routes.types.ts',
          '@environment': './environment.ts',
          '@services': './src/services',
          '@store': './src/store',
          '@database': './src/database',
          '@layouts': './src/layouts/index.ts',
        },
      },
    ],
  ],
};
