{"name": "coleta-cacau", "version": "2.0.0-rc.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@react-native-async-storage/async-storage": "^1.19.3", "@react-native-community/netinfo": "^11.3.2", "@react-native-picker/picker": "^2.6.1", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/native": "^6.1.7", "@react-navigation/stack": "^6.3.17", "@realm/react": "^0.6.2", "@reduxjs/toolkit": "^1.9.7", "@sentry/react-native": "^5.20.0", "appcenter": "5.0.1", "appcenter-analytics": "5.0.1", "appcenter-crashes": "5.0.1", "axios": "^1.5.0", "core-js-pure": "^3.34.0", "jwt-decode": "^4.0.0", "react": "18.2.0", "react-native": "0.72.4", "react-native-bootsplash": "^5.1.3", "react-native-device-info": "^10.12.0", "react-native-gesture-handler": "^2.13.0", "react-native-localize": "^3.0.4", "react-native-safe-area-context": "^4.7.4", "react-native-screens": "^3.25.0", "react-native-snackbar": "^2.6.2", "react-native-svg": "^14.1.0", "react-redux": "^8.1.3", "realm": "^12.3.0", "redux": "^4.2.1", "redux-persist": "^6.0.0", "styled-components": "^6.1.8"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/eslint-config": "^0.72.2", "@react-native/metro-config": "^0.72.11", "@tsconfig/react-native": "^3.0.0", "@types/react": "^18.0.24", "@types/react-native-dotenv": "^0.2.0", "@types/react-redux": "^7.1.28", "@types/react-test-renderer": "^18.0.0", "@types/redux-persist": "^4.3.1", "@types/styled-components": "^5.1.27", "babel-jest": "^29.2.1", "babel-plugin-module-resolver": "^5.0.0", "babel-plugin-styled-components": "^2.1.4", "eslint": "^8.19.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.76.8", "prettier": "^2.4.1", "react-native-dotenv": "^3.4.9", "react-native-svg-transformer": "^1.1.0", "react-test-renderer": "18.2.0", "typescript": "4.8.4"}, "engines": {"node": ">=16"}}